'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  FileText,
  Inbox,
  Users,
  MessageCircle,
  ChevronDown,
  User,
  Settings,
  Euro,
  CheckCircle,
  Send,
  LogOut,
} from 'lucide-react';
import { FiTarget, FiMessageSquare } from 'react-icons/fi';
import { RiContractLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';
import { useAuth } from '@/contexts/AuthContext';
import { getDisplayName, getInitials } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface DesktopHeaderProps {
  userType: 'influencer' | 'business';
  profile: any;
}

interface NavItem {
  name: string;
  href: string;
  icon: any;
}

interface ProfileMenuItem {
  name: string;
  href: string;
  icon: any;
  description?: string;
}

export function DesktopHeader({ userType, profile }: DesktopHeaderProps) {
  const pathname = usePathname();
  const { signOut } = useAuth();

  // Main navigation items for business users
  const businessNavigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard/biznis', icon: LayoutDashboard },
    { name: 'Kampanje', href: '/dashboard/campaigns', icon: FiTarget },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: RiContractLine,
    },
    { name: 'Influenceri', href: '/marketplace/influencers', icon: Users },
    { name: 'Moje ponude', href: '/dashboard/biznis/offers', icon: Send },
    { name: 'Poruke', href: '/dashboard/chat', icon: FiMessageSquare },
  ];

  // Main navigation items for influencer users
  const influencerNavigation: NavItem[] = [
    { name: 'Dashboard', href: '/dashboard/influencer', icon: LayoutDashboard },
    { name: 'Ponude', href: '/dashboard/influencer/offers', icon: Inbox },
    { name: 'Kampanje', href: '/marketplace/campaigns', icon: FileText },
    {
      name: 'Moje aplikacije',
      href: '/dashboard/influencer/applications',
      icon: FileText,
    },
    { name: 'Poruke', href: '/dashboard/chat', icon: MessageCircle },
  ];

  // Profile dropdown items for business users
  const businessProfileItems: ProfileMenuItem[] = [
    {
      name: 'Postavke računa',
      href: '/dashboard/biznis/account',
      icon: Settings,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Moj profil',
      href: '/dashboard/biznis/profile',
      icon: User,
      description: 'Javni profil firme',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Profile dropdown items for influencer users
  const influencerProfileItems: ProfileMenuItem[] = [
    {
      name: 'Postavke računa',
      href: '/dashboard/influencer/account',
      icon: Settings,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Moj profil',
      href: '/dashboard/influencer/profile',
      icon: User,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: Euro,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const navigation =
    userType === 'business' ? businessNavigation : influencerNavigation;
  const profileItems =
    userType === 'business' ? businessProfileItems : influencerProfileItems;

  const isActive = (href: string) => {
    if (href === '/dashboard/influencer' || href === '/dashboard/biznis') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/50 bg-background/95 backdrop-blur-md supports-[backdrop-filter]:bg-background/60 shadow-sm">
      <div className="flex h-16 items-center px-6 w-full">
        {/* Logo */}
        <div className="flex items-center">
          <Link href="/" className="flex items-center space-x-2 group">
            <Image
              src="/images/influexus_logo.webp"
              alt="Influexus Logo"
              width={32}
              height={32}
              className="rounded-lg transition-transform duration-300 group-hover:scale-110"
            />
            <h1 className="text-2xl font-bold gradient-text transition-all duration-300 group-hover:drop-shadow-sm">Influexus</h1>
          </Link>
        </div>

        {/* Centered Navigation - Hidden on mobile */}
        <nav className="hidden md:flex items-center justify-center flex-1 space-x-2">
          {navigation.map(item => (
            <Link key={item.name} href={item.href}>
              <Button
                variant="ghost"
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-all duration-300 ease-in-out rounded-xl relative overflow-hidden group',
                  isActive(item.href)
                    ? 'text-white bg-gradient-to-r from-[#7F5BFE] via-[#F35BF6] to-[#f04a13] shadow-lg shadow-purple-500/25 font-semibold'
                    : 'text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 hover:shadow-md hover:scale-105'
                )}
              >
                <item.icon className={cn(
                  "h-4 w-4 transition-all duration-300",
                  isActive(item.href) ? "drop-shadow-sm" : "group-hover:scale-110"
                )} />
                <span className={cn(
                  "transition-all duration-300",
                  isActive(item.href) ? "drop-shadow-sm" : ""
                )}>{item.name}</span>
                {isActive(item.href) && (
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-20 rounded-xl" />
                )}
              </Button>
            </Link>
          ))}
        </nav>

        {/* Right side - Notifications & Profile */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <NotificationDropdown />

          {/* Profile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center space-x-2 px-3 py-2 rounded-xl hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 transition-all duration-300 hover:shadow-md group"
              >
                <Avatar className="h-8 w-8 ring-2 ring-transparent group-hover:ring-purple-500/30 transition-all duration-300">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback className="bg-gradient-to-br from-[#7F5BFE] to-[#F35BF6] text-white font-semibold">
                    {getInitials(
                      getDisplayName(profile) !== 'Ime i prezime skriveno'
                        ? getDisplayName(profile)
                        : profile?.username
                    )}
                  </AvatarFallback>
                </Avatar>
                <ChevronDown className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors duration-300" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 bg-card/95 backdrop-blur-md border border-border/50 shadow-xl">
              <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-accent/20 to-accent/10 rounded-t-lg">
                <Avatar className="h-10 w-10 ring-2 ring-purple-500/30">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback className="bg-gradient-to-br from-[#7F5BFE] to-[#F35BF6] text-white font-semibold">
                    {getInitials(
                      getDisplayName(profile) !== 'Ime i prezime skriveno'
                        ? getDisplayName(profile)
                        : profile?.username
                    )}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-semibold text-foreground">
                    {getDisplayName(profile) !== 'Ime i prezime skriveno'
                      ? getDisplayName(profile)
                      : profile?.username}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    @{profile?.username}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator className="bg-border/50" />
              {profileItems.map(item => (
                <DropdownMenuItem
                  key={item.name}
                  asChild
                  className="cursor-pointer hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 transition-all duration-300 mx-1 my-1 rounded-lg"
                >
                  <Link href={item.href} className="flex items-center p-2">
                    <item.icon className="mr-3 h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">{item.name}</span>
                      {item.description && (
                        <span className="text-xs text-muted-foreground">{item.description}</span>
                      )}
                    </div>
                  </Link>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator className="bg-border/50" />
              <DropdownMenuItem
                onClick={handleSignOut}
                className="cursor-pointer text-red-600 hover:bg-red-50 hover:text-red-700 dark:hover:bg-red-950/50 transition-all duration-300 mx-1 my-1 rounded-lg"
              >
                <LogOut className="mr-3 h-4 w-4" />
                <span className="font-medium">Odjavi se</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
