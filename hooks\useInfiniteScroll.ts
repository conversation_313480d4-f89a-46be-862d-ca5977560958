'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { InfiniteScrollCache } from '@/components/ui/infinite-scroll';

interface UseInfiniteScrollOptions<T> {
  fetchData: (offset: number, limit: number) => Promise<{ data: T[]; hasMore: boolean; error?: unknown }>;
  limit?: number;
  cacheKey?: string;
  dependencies?: unknown[];
  initialData?: T[];
}

export function useInfiniteScroll<T>({
  fetchData,
  limit = 20,
  cacheKey,
  dependencies = [],
  initialData = []
}: UseInfiniteScrollOptions<T>) {
  const [data, setData] = useState<T[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<unknown>(null);
  const offsetRef = useRef(0);
  const isInitialLoad = useRef(true);

  // Reset funkcija kada se promene filters/dependencies
  const reset = useCallback(async () => {
    // Obriši cache ako se menjaju filteri
    if (cacheKey) {
      InfiniteScrollCache.clearCache(cacheKey);
    }
    
    setData([]);
    setHasMore(true);
    setError(null);
    offsetRef.current = 0;
    isInitialLoad.current = true;
    
    // Učitaj prva data
    await loadInitialData();
  }, [cacheKey]);

  // Učitaj prva data
  const loadInitialData = useCallback(async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      // Proverava cache ako postoji cacheKey
      let cachedData: T[] = [];
      if (cacheKey && isInitialLoad.current) {
        cachedData = InfiniteScrollCache.loadData(cacheKey);
        if (cachedData.length > 0) {
          setData(cachedData);
          offsetRef.current = cachedData.length;
          setIsLoading(false);
          return;
        }
      }

      const result = await fetchData(0, limit);
      
      if (result.error) {
        setError(result.error);
        return;
      }

      const newData = result.data || [];
      setData(newData);
      setHasMore(result.hasMore);
      offsetRef.current = newData.length;

      // Cache nova data
      if (cacheKey) {
        InfiniteScrollCache.saveData(cacheKey, newData);
      }

    } catch (err) {
      console.error('Error loading initial data:', err);
      setError(err);
    } finally {
      setIsLoading(false);
      isInitialLoad.current = false;
    }
  }, [fetchData, limit, cacheKey, isLoading]);

  // Load more data
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore || isLoading) return;

    try {
      setIsLoadingMore(true);
      setError(null);

      const result = await fetchData(offsetRef.current, limit);
      
      if (result.error) {
        setError(result.error);
        return;
      }

      const newData = result.data || [];
      
      setData(prevData => {
        const updatedData = [...prevData, ...newData];
        
        // Cache updated data
        if (cacheKey) {
          InfiniteScrollCache.saveData(cacheKey, updatedData);
        }
        
        return updatedData;
      });
      
      setHasMore(result.hasMore);
      offsetRef.current += newData.length;

    } catch (err) {
      console.error('Error loading more data:', err);
      setError(err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetchData, limit, hasMore, isLoadingMore, isLoading, cacheKey]);

  // Refresh - učitaj podatke ponovo
  const refresh = useCallback(async () => {
    if (cacheKey) {
      InfiniteScrollCache.clearCache(cacheKey);
    }
    
    setData([]);
    offsetRef.current = 0;
    setHasMore(true);
    setError(null);
    isInitialLoad.current = true;
    
    await loadInitialData();
  }, [loadInitialData, cacheKey]);

  // Auto-load na promenu dependencies
  useEffect(() => {
    reset();
  }, dependencies);

  // Initial load
  useEffect(() => {
    if (data.length === 0 && !isLoading && isInitialLoad.current) {
      loadInitialData();
    }
  }, [data.length, isLoading, loadInitialData]);

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    actions: {
      loadMore,
      reset,
      refresh
    }
  };
}

// Hook za cache-iranje filtera
export function useCachedFilters<T>(cacheKey: string, initialFilters: T) {
  const [filters, setFilters] = useState<T>(() => {
    if (typeof window === 'undefined') return initialFilters;
    
    const cached = InfiniteScrollCache.loadFilters(cacheKey);
    return cached || initialFilters;
  });

  const updateFilters = useCallback((newFilters: T) => {
    setFilters(newFilters);
    if (cacheKey) {
      InfiniteScrollCache.saveFilters(cacheKey, newFilters);
    }
  }, [cacheKey]);

  return [filters, updateFilters] as const;
}