globals.css:

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens to match Instagram-themed design brief */
  --background: oklch(1 0 0);
  --foreground: oklch(0.4 0.01 258.34);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.4 0.01 258.34);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.4 0.01 258.34);
  --primary: oklch(0.45 0.24 15.34);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.98 0.01 15.34);
  --secondary-foreground: oklch(0.4 0.01 258.34);
  --muted: oklch(0.98 0.01 15.34);
  --muted-foreground: oklch(0.4 0.01 258.34);
  --accent: oklch(0.65 0.25 340.89);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0.005 258.34);
  --input: oklch(0.95 0.005 258.34);
  --ring: oklch(0.45 0.24 15.34 / 0.5);
  --chart-1: oklch(0.45 0.24 15.34);
  --chart-2: oklch(0.65 0.25 340.89);
  --chart-3: oklch(0.98 0.01 15.34);
  --chart-4: oklch(0.4 0.01 258.34);
  --chart-5: oklch(0.35 0.01 258.34);
  --radius: 0.5rem;
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.4 0.01 258.34);
  --sidebar-primary: oklch(0.45 0.24 15.34);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.65 0.25 340.89);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.92 0.005 258.34);
  --sidebar-ring: oklch(0.45 0.24 15.34 / 0.5);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}




badge.tsx:
import type * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />
}

export { Badge, badgeVariants }



PAGE.TSX:
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check } from "lucide-react"

export function PricingPage() {
  const plans = [
    {
      name: "FREE",
      price: "€0",
      period: "forever",
      description: "Perfect for getting started",
      features: [
        "Up to 3 brand connections",
        "Basic analytics",
        "Email support",
        "Standard profile",
        "Mobile app access",
      ],
      buttonText: "Get Started",
      buttonVariant: "outline" as const,
      popular: false,
    },
    {
      name: "Premium",
      price: "€49,99",
      period: "per month",
      description: "Everything you need to grow",
      features: [
        "Unlimited brand connections",
        "Advanced analytics & insights",
        "Priority support",
        "Verified profile badge",
        "Campaign management tools",
        "Custom media kit",
        "Revenue tracking",
        "Brand collaboration tools",
      ],
      buttonText: "Start Premium",
      buttonVariant: "default" as const,
      popular: true,
    },
    {
      name: "Enterprise",
      price: "Coming Soon",
      period: "",
      description: "For agencies and large creators",
      features: [
        "Everything in Premium",
        "Team collaboration",
        "White-label solutions",
        "API access",
        "Dedicated account manager",
        "Custom integrations",
      ],
      buttonText: "Join Waitlist",
      buttonVariant: "outline" as const,
      popular: false,
      comingSoon: true,
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary via-accent to-primary">
      <div className="container mx-auto px-4 py-16">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 text-balance drop-shadow-lg">
            Choose Your Plan
          </h1>
          <p className="text-xl text-white/90 max-w-2xl mx-auto text-pretty drop-shadow-md">
            Find the perfect fit for your influence. Connect with brands, grow your audience, and monetize your content.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => {
            const getCardGradient = (index: number) => {
              switch (index) {
                case 0:
                  return "bg-gradient-to-b from-[#FCCBF0] via-[#FF5A57] via-[#E02F75] to-[#6700A3]"
                case 1:
                  return "bg-gradient-to-b from-[#FF5A57] via-[#E02F75] via-[#6700A3] to-[#050C38]"
                case 2:
                  return "bg-gradient-to-b from-[#6700A3] via-[#1B2062] to-[#050C38]"
                default:
                  return "bg-card/95"
              }
            }

            const getTextClasses = (index: number) => {
              switch (index) {
                case 0:
                  return {
                    title: "text-white drop-shadow-lg font-black tracking-tight",
                    description: "text-white/95 drop-shadow-md font-semibold",
                    price: "text-white drop-shadow-lg font-black tracking-tighter",
                    period: "text-white/90 drop-shadow-md font-medium",
                    feature: "text-white/95 drop-shadow-sm font-medium",
                  }
                case 1:
                case 2:
                  return {
                    title: "text-white drop-shadow-lg font-black tracking-tight",
                    description: "text-white/95 drop-shadow-md font-semibold",
                    price: "text-white drop-shadow-lg font-black tracking-tighter",
                    period: "text-white/90 drop-shadow-md font-medium",
                    feature: "text-white/95 drop-shadow-sm font-medium",
                  }
                default:
                  return {
                    title: "text-white drop-shadow-lg font-black tracking-tight",
                    description: "text-white/95 drop-shadow-md font-semibold",
                    price: "text-white drop-shadow-lg font-black tracking-tighter",
                    period: "text-white/90 drop-shadow-md font-medium",
                    feature: "text-white/95 drop-shadow-sm font-medium",
                  }
              }
            }

            const textClasses = getTextClasses(index)

            return (
              <Card
                key={plan.name}
                className={`relative transition-all duration-300 hover:scale-105 ${getCardGradient(index)} backdrop-blur-sm ${
                  plan.popular ? "border-accent shadow-2xl scale-105" : "hover:shadow-xl"
                } ${plan.comingSoon ? "opacity-90" : ""} flex flex-col h-full`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 right-4 bg-white/20 text-white px-3 py-1 text-sm font-semibold backdrop-blur-sm drop-shadow-md">
                    Most Popular
                  </Badge>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className={`text-3xl ${textClasses.title}`}>{plan.name}</CardTitle>
                  <CardDescription className={textClasses.description}>{plan.description}</CardDescription>
                </CardHeader>

                <CardContent className="text-center pb-6 flex-1 flex flex-col">
                  <div className="mb-6">
                    <span className={`text-5xl ${textClasses.price} ${plan.comingSoon ? "opacity-70" : ""}`}>
                      {plan.price}
                    </span>
                    {plan.period && <span className={`${textClasses.period} ml-2`}>{plan.period}</span>}
                  </div>

                  <ul className="space-y-3 text-left flex-1">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <Check className="w-5 h-5 mt-0.5 flex-shrink-0 text-white drop-shadow-sm" />
                        <span className={`text-sm ${textClasses.feature}`}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>

                <CardFooter className="mt-auto">
                  <Button
                    className={`w-full font-bold text-base ${
                      plan.popular
                        ? "bg-white/25 hover:bg-white/35 text-white border-white/40 shadow-lg"
                        : "bg-white/20 hover:bg-white/30 text-white border-white/30 shadow-md"
                    } ${plan.comingSoon ? "opacity-75" : ""} backdrop-blur-sm drop-shadow-md transition-all duration-200`}
                    variant="outline"
                    disabled={plan.comingSoon}
                  >
                    {plan.buttonText}
                  </Button>
                </CardFooter>
              </Card>
            )
          })}
        </div>

        {/* Footer Section */}
        <div className="text-center mt-16">
          <p className="text-white/80 mb-4 drop-shadow-sm">All plans include our core features and mobile app access</p>
          <div className="flex justify-center gap-6 text-sm">
            <a href="#" className="text-white/70 hover:text-white transition-colors drop-shadow-sm">
              Terms of Service
            </a>
            <a href="#" className="text-white/70 hover:text-white transition-colors drop-shadow-sm">
              Privacy Policy
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
