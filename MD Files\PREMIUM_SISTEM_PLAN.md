# 💎 PREMIUM/FREE SISTEM - IMPLEMENTACIJSKI PLAN

**Kreiran**: 10.08.2025  
**Status**: BUDUĆE PLANIRANJE  
**Prioritet**: NIZAK

---

## 🎯 **STRATEGIJA IMPLEMENTACIJE**

**Preporučeni pristup**: Prvo napraviti sve funkcionalnosti kao da su svi korisnici premium, zatim postupno zaključavati funkcionalnosti za free korisnike.

**Razlog**: Lak<PERSON>e je razvijati i testirati kompletnu funkcionalnost, a zatim dodavati ograničenja, nego obrnuto.

---

## 👥 **FREE vs PREMIUM KORISNICI**

### **FREE INFLUENCER** 🆓
- ✅ **3x mjesečno** prijaviti na kampanju/posao/oglas
- ❌ **Nema ispod pricing paketa button "posaljite ponudu"** - za custom ponude
- ❌ Nema VERIFIED badge
- ❌ Prikazuje se nakon verifikovanih u listama

### **PREMIUM INFLUENCER** 💎 = 19,99€/mjesec
- ✅ **Neograničene prijave** na kampanje/posao/oglas
- ✅ **Ima ispod pricing paketa button "posaljite ponudu"** - za custom ponude
- ✅ **VERIFIED BADGE** - prikazuje se kao verifikovan
- ✅ **Prioritet u listama** - uvijek se prikazuje prvi u svim filterima

### **FREE BIZNIS** 🆓
- ✅ **30€ naknada** za kreiranje/aktiviranje kampanje
- ❌ **Nema ispod pricing paketa influencera button "posaljite ponudu"** - za custom ponude
- ❌ Nema VERIFIED badge
- ❌ Ne može kreirati kampanje samo za verifikovane
- ❌ Ne vidi handles društvenih mreža
- ❌ Ograničeni filteri

### **PREMIUM BIZNIS** 💎 = 49,99€/mjesec
- ✅ **Neograničeno kreiranje kampanja** (bez naknade)
- ✅ **Ima ispod pricing paketa influencera button "posaljite ponudu"** - za custom ponude
- ✅ **VERIFIED BADGE** - prikazuje se kao verifikovan
- ✅ **Ekskluzivne kampanje** - može kreirati kampanje koje vide samo verifikovani influenceri
- ✅ **Pristup handle-ima** - vidi i može kliknuti na društvene mreže influencera
- ✅ **Napredni filteri** - po kategoriji, verifikovanim influencerima, itd.

---

## 🏗️ **IMPLEMENTACIJSKI PLAN**

### **FAZA 1: Database Schema** 📊
```sql
-- Dodati subscription polja u profiles tabelu
ALTER TABLE profiles ADD COLUMN subscription_type VARCHAR(20) DEFAULT 'free';
ALTER TABLE profiles ADD COLUMN subscription_expires_at TIMESTAMP;
ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT false;

-- Dodati tracking za mjesečne limite
CREATE TABLE user_monthly_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  month_year VARCHAR(7), -- format: '2025-08'
  campaign_applications_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### **FAZA 2: UI Komponente** 🎨
- **Verified Badge komponenta** - za prikaz verifikovanog statusa
- **Premium Lock komponenta** - za zaključane funkcionalnosti
- **Subscription Status komponenta** - prikaz trenutnog plana
- **Upgrade Prompt komponenta** - poziv na upgrade

### **FAZA 3: Business Logic** 🔧
- **Subscription Service** - provjera premium statusa
- **Limits Service** - praćenje mjesečnih limita
- **Verification Service** - upravljanje verified badge-om
- **Payment Integration** - Stripe/PayPal za premium upgrade

### **FAZA 4: Feature Gating** 🚪
- **Campaign Applications** - limit za free influencere
- **Campaign Creation** - naknada za free biznise
- **Advanced Filters** - samo za premium biznise
- **Social Media Links** - samo za premium biznise
- **Exclusive Campaigns** - samo premium biznis → verified influenceri

### **FAZA 5: Prioritization Logic** 📈
- **Search Results** - verified influenceri uvijek prvi
- **Campaign Listings** - verified biznisi uvijek prvi
- **Recommendation Engine** - prioritet verified korisnicima

---

## 💻 **TEHNIČKA IMPLEMENTACIJA**

### **Hook za Subscription Check**
```typescript
// useSubscription.ts
export const useSubscription = () => {
  const { user } = useAuth();

  return {
    isPremium: user?.subscription_type === 'premium',
    isVerified: user?.is_verified || false,
    canApplyToCampaign: () => checkMonthlyLimit(user?.id),
    canCreateCampaign: () => user?.subscription_type === 'premium',
    canViewSocialHandles: () => user?.subscription_type === 'premium'
  };
};
```

### **Premium Gate Komponenta**
```typescript
// PremiumGate.tsx
export const PremiumGate = ({
  feature,
  children,
  fallback
}: PremiumGateProps) => {
  const { isPremium } = useSubscription();

  if (!isPremium) {
    return fallback || <UpgradePrompt feature={feature} />;
  }

  return children;
};
```

### **Verified Badge Komponenta**
```typescript
// VerifiedBadge.tsx
export const VerifiedBadge = ({ 
  isVerified, 
  size = 'sm' 
}: VerifiedBadgeProps) => {
  if (!isVerified) return null;

  return (
    <div className={`inline-flex items-center gap-1 ${
      size === 'sm' ? 'text-xs' : 'text-sm'
    }`}>
      <CheckCircle className={`${
        size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'
      } text-blue-500`} />
      <span className="text-blue-600 font-medium">Verified</span>
    </div>
  );
};
```

### **Monthly Limits Service**
```typescript
// monthlyLimits.ts
export const checkMonthlyLimit = async (userId: string): Promise<boolean> => {
  const currentMonth = new Date().toISOString().slice(0, 7); // '2025-08'
  
  const { data: limits } = await supabase
    .from('user_monthly_limits')
    .select('campaign_applications_count')
    .eq('user_id', userId)
    .eq('month_year', currentMonth)
    .single();

  const { data: profile } = await supabase
    .from('profiles')
    .select('subscription_type')
    .eq('id', userId)
    .single();

  // Premium korisnici nemaju limite
  if (profile?.subscription_type === 'premium') return true;

  // Free korisnici imaju limit od 1 aplikacije mjesečno
  return (limits?.campaign_applications_count || 0) < 1;
};
```

---

## 📅 **PROCJENA VREMENA**

- **Faza 1**: 1 dan (database schema)
- **Faza 2**: 2-3 dana (UI komponente)
- **Faza 3**: 3-4 dana (business logic)
- **Faza 4**: 2-3 dana (feature gating)
- **Faza 5**: 1-2 dana (prioritization)

**Ukupno**: 9-13 radnih dana

---

## 💰 **PRICING MODEL PREDLOG**

### **INFLUENCER PLANOVI**
- **Free**: 1 aplikacija mjesečno, osnovni profil
- **Pro (15€/mjesec)**: Neograničene aplikacije, verified badge, prioritet u listama
- **Premium (25€/mjesec)**: Sve Pro funkcije + advanced analytics + priority support

### **BIZNIS PLANOVI**
- **Free**: 30€ po kampanji, osnovni filteri, nema pristup handle-ima
- **Pro (50€/mjesec)**: Neograničene kampanje, verified badge, pristup handle-ima
- **Enterprise (150€/mjesec)**: Sve Pro funkcije + ekskluzivne kampanje + advanced analytics

---

## 🎯 **PRIORITET IMPLEMENTACIJE**

**Trenutno**: NIZAK prioritet - fokus na osnovnu funkcionalnost  
**Buduće**: Kada imamo stabilnu bazu korisnika (100+ aktivnih korisnika)  
**Preduslov**: Svi kritični i visoki prioritet taskovi moraju biti riješeni

---

*Poslednje ažuriranje: 10.08.2025 - Kreiran plan za buduću implementaciju*
