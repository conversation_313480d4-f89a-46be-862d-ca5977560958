// Redesigned email templates matching your account page design system

export function generatePasswordResetTemplate(
  resetUrl: string,
  userEmail: string
) {
  const htmlContent = `
<!DOCTYPE html>
<html lang="sr" style="margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset<PERSON><PERSON><PERSON> - INFLUEXUS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .header {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.6) 100%);
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
        }
        
        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: white;
        }
        
        .main-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.03) 100%);
            margin: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.01) 100%);
        }
        
        .content {
            position: relative;
            padding: 40px 32px;
            text-align: center;
        }
        
        .icon-container {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.05) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }
        
        .greeting {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 32px;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }
        
        .warning {
            background: rgba(254, 243, 199, 0.8);
            border: 1px solid rgba(251, 191, 36, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
        }
        
        .warning-text {
            font-size: 14px;
            color: #92400e;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alternative-section {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-top: 24px;
        }
        
        .alternative-text {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
        }
        
        .alternative-link {
            background: white;
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            color: #64748b;
            word-break: break-all;
            font-family: 'Courier New', monospace;
        }
        
        .footer {
            background: rgba(248, 250, 252, 0.5);
            padding: 24px 32px;
            text-align: center;
            border-top: 1px solid rgba(102, 126, 234, 0.05);
        }
        
        .footer-text {
            font-size: 12px;
            color: #94a3b8;
            line-height: 1.6;
        }
        
        .footer-link {
            color: #667eea;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                border-radius: 0;
                margin: -20px;
            }
            
            .main-card {
                margin: 16px;
            }
            
            .content {
                padding: 32px 24px;
            }
            
            .greeting {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-text">INFLUEXUS</div>
            </div>
        </div>
        
        <div class="main-card">
            <div class="content">
                <h1 class="greeting">Resetovanje Lozinke</h1>
                <p class="subtitle">
                    Primili smo zahtev za resetovanje lozinke za vaš nalog.<br>
                    Kliknite na dugme ispod da biste kreirali novu lozinku.
                </p>
                
                <a href="${resetUrl}" class="reset-button">
                    Resetuj Lozinku
                </a>
                
                <div class="warning">
                    <p class="warning-text">
                        ⚠️ <strong>Napomena:</strong> Ovaj link je valjan 24 sata. Ako niste zatražili resetovanje lozinke, molimo vas da ignorišete ovaj email.
                    </p>
                </div>
                
                <div class="alternative-section">
                    <p class="alternative-text">
                        Ako dugme ne radi, kopirajte i zalepite ovaj link u vaš browser:
                    </p>
                    <div class="alternative-link">
                        ${resetUrl}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Ovaj email je poslat na <strong>${userEmail}</strong><br>
                Iz bezbednosnih razloga, ovaj link ističe za 24 sata.<br><br>
                <a href="#" class="footer-link">Uslovi korišćenja</a> | 
                <a href="#" class="footer-link">Politika privatnosti</a><br><br>
                © 2024 INFLUEXUS. Sva prava zadržana.
            </p>
        </div>
    </div>
</body>
</html>
  `;

  const textContent = `
INFLUEXUS - Resetovanje Lozinke

Primili smo zahtev za resetovanje lozinke za vaš nalog.
Kliknite na sledeći link da biste kreirali novu lozinku:

${resetUrl}

NAPOMENA: Ovaj link je valjan 24 sata. Ako niste zatražili resetovanje lozinke, molimo vas da ignorišete ovaj email.

Ovaj email je poslat na ${userEmail}
Iz bezbednosnih razloga, ovaj link ističe za 24 sata.

© 2024 INFLUEXUS. Sva prava zadržana.
  `;

  return { htmlContent, textContent };
}

export function generateWelcomeTemplate(
  userName: string,
  userEmail: string,
  userType: 'influencer' | 'business'
) {
  const isInfluencer = userType === 'influencer';

  // Different gradients for different user types
  const headerGradient = isInfluencer
    ? 'linear-gradient(135deg, rgba(240, 148, 51, 0.8) 0%, rgba(230, 104, 60, 0.6) 25%, rgba(220, 39, 67, 0.7) 50%, rgba(204, 35, 102, 0.6) 75%, rgba(188, 24, 136, 0.8) 100%)'
    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.6) 100%)';

  const cardGradient = isInfluencer
    ? 'linear-gradient(135deg, rgba(240, 148, 51, 0.05) 0%, rgba(230, 104, 60, 0.02) 25%, rgba(220, 39, 67, 0.04) 50%, rgba(204, 35, 102, 0.02) 75%, rgba(188, 24, 136, 0.05) 100%)'
    : 'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.03) 100%)';

  const buttonGradient = isInfluencer
    ? 'linear-gradient(135deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%)'
    : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

  const titleGradient = isInfluencer
    ? 'linear-gradient(135deg, #f09433 0%, #dc2743 50%, #bc1888 100%)'
    : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

  const welcomeMessage = isInfluencer
    ? 'Dobrodošli u zajednicu influensera!'
    : 'Dobrodošli u zajednicu brendova!';

  const subtitle = isInfluencer
    ? 'Vaš profil je kreiran i spremni ste da počnete sa povezivanjem sa brendovima.'
    : 'Vaš profil je kreiran i spremni ste da počnete sa pronalaženjem influensera.';

  const ctaText = isInfluencer ? 'Pogledaj Kampanje' : 'Kreiraj Kampanju';
  const ctaUrl = isInfluencer
    ? '/dashboard/marketplace/campaigns'
    : '/dashboard/campaigns';

  const htmlContent = `
<!DOCTYPE html>
<html lang="sr" style="margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dobrodošli - INFLUEXUS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .header {
            background: ${headerGradient};
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }
        
        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: white;
        }
        
        .main-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            background: ${cardGradient};
            margin: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(147, 51, 234, 0.1);
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.02) 0%, rgba(236, 72, 153, 0.01) 100%);
        }
        
        .content {
            position: relative;
            padding: 40px 32px;
            text-align: center;
        }
        
        
        .greeting {
            font-size: 32px;
            font-weight: bold;
            background: ${titleGradient};
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        
        .user-name {
            font-size: 20px;
            color: #64748b;
            margin-bottom: 24px;
            font-weight: 500;
        }
        
        .subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .cta-button {
            display: inline-block;
            background: ${buttonGradient};
            color: white !important;
            padding: 16px 32px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
            transition: all 0.3s ease;
            margin-bottom: 32px;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(147, 51, 234, 0.3);
        }
        
        .features {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(147, 51, 234, 0.1);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            text-align: left;
        }
        
        .features-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .features-list {
            list-style: none;
        }
        
        .features-item {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 8px;
            padding-left: 24px;
            position: relative;
        }
        
        .features-item::before {
            content: '✅';
            position: absolute;
            left: 0;
        }
        
        .footer {
            background: rgba(248, 250, 252, 0.5);
            padding: 24px 32px;
            text-align: center;
            border-top: 1px solid rgba(147, 51, 234, 0.05);
        }
        
        .footer-text {
            font-size: 12px;
            color: #94a3b8;
            line-height: 1.6;
        }
        
        .footer-link {
            color: #7c3aed;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                border-radius: 0;
                margin: -20px;
            }
            
            .main-card {
                margin: 16px;
            }
            
            .content {
                padding: 32px 24px;
            }
            
            .greeting {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-text">INFLUEXUS</div>
            </div>
        </div>
        
        <div class="main-card">
            <div class="content">
                <h1 class="greeting">${welcomeMessage}</h1>
                <p class="user-name">Zdravo, ${userName}!</p>
                <p class="subtitle">${subtitle}</p>
                
                <a href="${ctaUrl}" class="cta-button">
                    ${ctaText}
                </a>
                
                <div class="features">
                    <h3 class="features-title">${isInfluencer ? 'Šta možete da radite:' : 'Šta možete da radite:'}</h3>
                    <ul class="features-list">
                        ${
                          isInfluencer
                            ? `
                        <li class="features-item">Pregledajte aktuelne kampanje</li>
                        <li class="features-item">Aplikujte za kampanje koje vam se sviđaju</li>
                        <li class="features-item">Komunicirajte sa brendovima</li>
                        <li class="features-item">Upravljajte svojom zaradkom</li>
                        `
                            : `
                        <li class="features-item">Kreirajte kampanje za svoj brend</li>
                        <li class="features-item">Pronađite savršene influensere</li>
                        <li class="features-item">Upravljajte saradnjama</li>
                        <li class="features-item">Pratite rezultate kampanja</li>
                        `
                        }
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Hvala što ste se pridružili INFLUEXUS zajednici!<br>
                Ovaj email je poslat na <strong>${userEmail}</strong><br><br>
                <a href="#" class="footer-link">Uslovi korišćenja</a> | 
                <a href="#" class="footer-link">Politika privatnosti</a><br><br>
                © 2024 INFLUEXUS. Sva prava zadržana.
            </p>
        </div>
    </div>
</body>
</html>
  `;

  const textContent = `
INFLUEXUS - ${welcomeMessage}

Zdravo, ${userName}!

${subtitle}

${isInfluencer ? 'Šta možete da radite:' : 'Šta možete da radite:'}
${
  isInfluencer
    ? `
- Pregledajte aktuelne kampanje
- Aplikujte za kampanje koje vam se sviđaju  
- Komunicirajte sa brendovima
- Upravljajte svojom zaradkom
`
    : `
- Kreirajte kampanje za svoj brend
- Pronađite savršene influensere
- Upravljajte saradnjama
- Pratite rezultate kampanja
`
}

Idite na: ${ctaUrl}

Hvala što ste se pridružili INFLUEXUS zajednici!
Ovaj email je poslat na ${userEmail}

© 2024 INFLUEXUS. Sva prava zadržana.
  `;

  return { htmlContent, textContent };
}

export function generateNotificationTemplate(
  userName: string,
  userEmail: string,
  notificationType:
    | 'new_offer'
    | 'new_order'
    | 'application_approved'
    | 'payment_required_offer'
    | 'payment_required_order'
    | 'work_submitted'
    | 'payment_completed'
    | 'work_approved',
  details: any
) {
  const typeConfig = {
    new_offer: {
      icon: '💼',
      title: 'Nova Direktna Ponuda!',
      gradient:
        'linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      titleGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    new_order: {
      icon: '📦',
      title: 'Nova Narudžba Paketa!',
      gradient:
        'linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(219, 39, 119, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(219, 39, 119, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #9333ea 0%, #db2777 100%)',
      titleGradient: 'linear-gradient(135deg, #9333ea 0%, #db2777 100%)',
    },
    application_approved: {
      icon: '✅',
      title: 'Aplikacija Prihvaćena!',
      gradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(21, 128, 61, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(21, 128, 61, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
      titleGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
    },
    payment_required_offer: {
      icon: '💳',
      title: 'Izvršite Plaćanje - Ponuda',
      gradient:
        'linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(194, 65, 12, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, rgba(194, 65, 12, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #f97316 0%, #c2410c 100%)',
      titleGradient: 'linear-gradient(135deg, #f97316 0%, #c2410c 100%)',
    },
    payment_required_order: {
      icon: '💳',
      title: 'Izvršite Plaćanje - Narudžba',
      gradient:
        'linear-gradient(135deg, rgba(249, 115, 22, 0.8) 0%, rgba(194, 65, 12, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(249, 115, 22, 0.05) 0%, rgba(194, 65, 12, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #f97316 0%, #c2410c 100%)',
      titleGradient: 'linear-gradient(135deg, #f97316 0%, #c2410c 100%)',
    },
    work_submitted: {
      icon: '📋',
      title: 'Rad Završen - Pregled Potreban!',
      gradient:
        'linear-gradient(135deg, rgba(168, 85, 247, 0.8) 0%, rgba(139, 92, 246, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(168, 85, 247, 0.05) 0%, rgba(139, 92, 246, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)',
      titleGradient: 'linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%)',
    },
    payment_completed: {
      icon: '💰',
      title: 'Plaćanje Izvršeno - Možete Početi!',
      gradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(21, 128, 61, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(21, 128, 61, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
      titleGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
    },
    work_approved: {
      icon: '🎉',
      title: 'Rad Odobren - Čestitamo!',
      gradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(21, 128, 61, 0.6) 100%)',
      cardGradient:
        'linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(21, 128, 61, 0.03) 100%)',
      buttonGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
      titleGradient: 'linear-gradient(135deg, #22c55e 0%, #15803d 100%)',
    },
  };

  const config = typeConfig[notificationType];

  const htmlContent = `
<!DOCTYPE html>
<html lang="sr" style="margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${config.title} - INFLUEXUS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .header {
            background: ${config.gradient};
            padding: 32px 24px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }
        
        .logo {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 1;
        }
        
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: white;
        }
        
        .main-card {
            position: relative;
            overflow: hidden;
            border-radius: 16px;
            background: ${config.cardGradient};
            margin: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(147, 51, 234, 0.1);
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.02) 0%, rgba(236, 72, 153, 0.01) 100%);
        }
        
        .content {
            position: relative;
            padding: 40px 32px;
            text-align: center;
        }
        
        
        .title {
            font-size: 32px;
            font-weight: bold;
            background: ${config.titleGradient};
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        
        .user-name {
            font-size: 20px;
            color: #64748b;
            margin-bottom: 24px;
            font-weight: 500;
        }
        
        .details {
            background: rgba(248, 250, 252, 0.8);
            border: 1px solid rgba(147, 51, 234, 0.1);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
        }
        
        .detail-item {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        }
        
        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #1e293b;
            display: inline-block;
            min-width: 100px;
        }
        
        .cta-button {
            display: inline-block;
            background: ${config.buttonGradient};
            color: white !important;
            padding: 16px 32px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 16px rgba(147, 51, 234, 0.2);
            transition: all 0.3s ease;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(147, 51, 234, 0.3);
        }
        
        .footer {
            background: rgba(248, 250, 252, 0.5);
            padding: 24px 32px;
            text-align: center;
            border-top: 1px solid rgba(147, 51, 234, 0.05);
        }
        
        .footer-text {
            font-size: 12px;
            color: #94a3b8;
            line-height: 1.6;
        }
        
        .footer-link {
            color: #7c3aed;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                border-radius: 0;
                margin: -20px;
            }
            
            .main-card {
                margin: 16px;
            }
            
            .content {
                padding: 32px 24px;
            }
            
            .title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">
                <div class="logo-text">INFLUEXUS</div>
            </div>
        </div>
        
        <div class="main-card">
            <div class="content">
                <h1 class="title">${config.title}</h1>
                <p class="user-name">Zdravo, ${userName}!</p>
                
                <div class="details">
                    ${Object.entries(details)
                      .map(
                        ([key, value]) => `
                        <div class="detail-item">
                            <span class="detail-label">${key}:</span> ${value}
                        </div>
                    `
                      )
                      .join('')}
                </div>
                
                <a href="/dashboard/notifications" class="cta-button">
                    Pogledaj Detaljnije
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Ovaj email je poslat na <strong>${userEmail}</strong><br>
                Da biste promenili postavke notifikacija, idite u Podešavanja naloga.<br><br>
                <a href="#" class="footer-link">Uslovi korišćenja</a> | 
                <a href="#" class="footer-link">Politika privatnosti</a><br><br>
                © 2024 INFLUEXUS. Sva prava zadržana.
            </p>
        </div>
    </div>
</body>
</html>
  `;

  const textContent = `
INFLUEXUS - ${config.title}

Zdravo, ${userName}!

${Object.entries(details)
  .map(([key, value]) => `${key}: ${value}`)
  .join('\n')}

Idite na: /dashboard/notifications

Ovaj email je poslat na ${userEmail}
Da biste promenili postavke notifikacija, idite u Podešavanja naloga.

© 2024 INFLUEXUS. Sva prava zadržana.
  `;

  return { htmlContent, textContent };
}
