# 🌍 Environment Setup Guide

## Overview

This guide explains how to properly configure environment variables for the Influencer Platform across different environments (development, staging, production).

## Quick Start

1. **Copy the example file**:
   ```bash
   cp .env.example .env.local
   ```

2. **Fill in your actual values** in `.env.local`

3. **Start the application**:
   ```bash
   npm run dev
   ```

The application will automatically validate your environment variables on startup.

## Environment Files Structure

```
├── .env.example          # Template with descriptions (commit to git)
├── .env.local           # Your local development config (DO NOT commit)
├── .env.development     # Development defaults (commit to git)
├── .env.staging         # Staging configuration (commit to git)
├── .env.production      # Production configuration template (commit to git)
```

## Required Environment Variables

### 🔵 Supabase Configuration
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiI...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiI...  # KEEP SECRET!
SUPABASE_ACCESS_TOKEN=sbp_your-access-token-here
```

**How to get these values:**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings → API
4. Copy the values from there

### 🔐 Webhook Secrets
```env
SUPABASE_WEBHOOK_SECRET=your-secure-webhook-secret-here
```

**How to generate:**
```bash
# Generate a secure random string
openssl rand -hex 32
```

### 💳 Stripe Configuration
```env
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...  # or pk_live_ for production
STRIPE_SECRET_KEY=sk_test_...                   # or sk_live_ for production
STRIPE_WEBHOOK_SECRET=whsec_...
```

**How to get these values:**
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/apikeys)
2. Copy your API keys
3. For webhook secret: go to Webhooks → select your endpoint → Signing secret

### 🌐 Application Configuration
```env
NEXT_PUBLIC_APP_URL=http://localhost:3000        # Development
NEXT_PUBLIC_APP_URL=https://yourdomain.com       # Production
NODE_ENV=development                             # or production, staging
```

## Environment-Specific Setup

### 🛠️ Development Setup

1. **Copy the development template**:
   ```bash
   cp .env.development .env.local
   ```

2. **Add your secrets** to `.env.local`:
   ```env
   # Add these to your .env.local file
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   SUPABASE_ACCESS_TOKEN=your-access-token
   SUPABASE_WEBHOOK_SECRET=your-webhook-secret
   
   # Stripe TEST keys for development
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

3. **Development features**:
   - Debug logging enabled
   - Relaxed CORS policy
   - Test Stripe keys
   - Optional email disabling

### 🧪 Staging Setup

Staging is used for testing production-like behavior.

1. **Set up staging environment**:
   ```env
   NODE_ENV=staging
   NEXT_PUBLIC_APP_URL=https://staging.yourdomain.com
   DEBUG=true
   LOG_REQUESTS=true
   ```

2. **Use test credentials** for external services
3. **Enable feature flags** for testing new features

### 🏭 Production Setup

Production requires strict security and monitoring.

1. **Environment variables** should be set in your deployment platform:
   - Vercel: Project Settings → Environment Variables
   - Netlify: Site Settings → Environment Variables
   - Railway: Variables tab
   - AWS/Google Cloud: Through their console

2. **Production checklist**:
   - ✅ Use LIVE Stripe keys (`pk_live_`, `sk_live_`)
   - ✅ Set `NODE_ENV=production`
   - ✅ Configure `SENTRY_DSN` for error tracking
   - ✅ Set `DEBUG=false`
   - ✅ Use production domain in `NEXT_PUBLIC_APP_URL`
   - ✅ Configure monitoring (`NEXT_PUBLIC_GA_TRACKING_ID`)
   - ✅ Set up secure webhook secrets

## Environment Validation

The application automatically validates environment variables on startup:

### ✅ Validation Features
- **Required variables**: Checks all mandatory environment variables
- **Format validation**: Validates URLs, JWTs, email addresses
- **Environment consistency**: Warns about test keys in production
- **Security checks**: Ensures sensitive variables are properly configured

### 🚨 Common Validation Errors

**Missing required variable:**
```
❌ NEXT_PUBLIC_SUPABASE_URL is required but not provided
```
**Solution**: Add the missing variable to your `.env.local` file

**Invalid format:**
```
❌ NEXT_PUBLIC_SUPABASE_URL must be a valid URL
```
**Solution**: Ensure the URL is properly formatted (starts with https://)

**JWT format error:**
```
❌ SUPABASE_SERVICE_ROLE_KEY must be a valid JWT token
```
**Solution**: Copy the complete JWT token from Supabase dashboard

## Security Best Practices

### 🔐 Secrets Management
- **NEVER** commit `.env.local` or any file with real secrets
- **ALWAYS** use environment variables for sensitive data
- **ROTATE** secrets periodically (webhooks, API keys)
- **USE** different secrets for different environments

### 🛡️ Environment Isolation
```
Development → Use test/sandbox APIs
Staging     → Use test/sandbox APIs with production-like setup
Production  → Use live/production APIs
```

### 🔍 Monitoring
- Set up error tracking with Sentry
- Enable application monitoring
- Monitor webhook deliveries
- Track API rate limits

## Deployment-Specific Instructions

### Vercel Deployment
```bash
# Install Vercel CLI
npm i -g vercel

# Set environment variables
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
# ... add all required variables

# Deploy
vercel --prod
```

### Netlify Deployment
1. Go to Site Settings → Environment Variables
2. Add all required variables
3. Deploy from Git

### Docker Deployment
```dockerfile
# In your Dockerfile
ENV NODE_ENV=production
ENV NEXT_PUBLIC_APP_URL=https://yourdomain.com
# Copy other environment variables from your deployment system
```

## Health Check

The application includes a health check endpoint that validates configuration:

```bash
# Check application health
curl http://localhost:3000/api/health

# Response example
{
  "status": "healthy",
  "uptime": 123456,
  "environment": "development",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "checks": {
    "environmentValid": true,
    "supabaseConfigured": true,
    "stripeConfigured": true,
    "webhookSecretsConfigured": true
  }
}
```

## Troubleshooting

### Application won't start
1. **Check environment validation errors** in console
2. **Verify all required variables** are set
3. **Check variable formats** (URLs, JWTs, etc.)
4. **Compare with `.env.example`**

### Webhook issues
1. **Verify webhook secrets** are correctly set
2. **Check webhook endpoints** in Stripe/Supabase dashboards
3. **Test webhook delivery** using dashboard tools

### Database connection issues
1. **Verify Supabase URL and keys**
2. **Check network connectivity**
3. **Verify RLS policies** in Supabase

### Stripe payment issues
1. **Verify Stripe keys** match environment (test/live)
2. **Check webhook configuration**
3. **Verify payment methods** are properly configured

## Migration from Old Setup

If you're migrating from an older environment setup:

1. **Backup existing `.env.local`**
2. **Copy new `.env.example` to `.env.local`**
3. **Transfer your values** to new format
4. **Run application** to validate
5. **Fix any validation errors**

## Support

If you encounter issues:
1. Check the validation error messages
2. Compare with `.env.example`
3. Verify credentials in respective dashboards
4. Check the application logs
5. Use the health check endpoint for diagnostics