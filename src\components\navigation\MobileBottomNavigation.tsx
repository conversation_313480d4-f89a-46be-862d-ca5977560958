'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  FileText,
  Inbox,
  MessageCircle,
  User,
  Send,
} from 'lucide-react';
import { FiTarget, FiMessageSquare } from 'react-icons/fi';
import { RiContractLine } from 'react-icons/ri';

interface MobileBottomNavigationProps {
  userType: 'influencer' | 'business';
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

export function MobileBottomNavigation({
  userType,
}: MobileBottomNavigationProps) {
  const pathname = usePathname();

  // Glavne 5 ikonice za influencere (dodana "Moje aplikacije")
  const influencerMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/influencer',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      href: '/dashboard/influencer/offers',
      icon: Inbox,
      label: '<PERSON>nu<PERSON>',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      href: '/marketplace/campaigns',
      icon: FiTarget,
      label: 'Kampanje',
    },
    {
      name: 'Moje aplikacije',
      href: '/dashboard/influencer/applications',
      icon: RiContractLine,
      label: 'Aplikacije',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: FiMessageSquare,
      label: 'Poruke',
    },
  ];

  // Glavne 6 ikonice za biznis korisnike (dodani "Moje ponude")
  const businessMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/biznis',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: 'Kampanje',
      href: '/dashboard/campaigns',
      icon: FiTarget,
      label: 'Kampanje',
    },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: RiContractLine,
      label: 'Aplikacije',
    },
    {
      name: 'Influenceri',
      href: '/marketplace/influencers',
      icon: User,
      label: 'Influenceri',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      label: 'Ponude',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: FiMessageSquare,
      label: 'Poruke',
    },
  ];

  const mainNavigation =
    userType === 'influencer' ? influencerMainNav : businessMainNav;

  const isActive = (href: string) => {
    // Exact match for dashboard home pages
    if (href === '/dashboard/influencer' || href === '/dashboard/biznis') {
      return pathname === href;
    }

    // For other routes, check if pathname starts with href
    return pathname === href || pathname.startsWith(href + '/');
  };

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 nav-bar-gradient-subtle backdrop-blur-md border-t border-purple-500/20 md:hidden shadow-lg shadow-purple-500/10">
        <div className="flex items-center justify-around py-2 px-1">
          {/* Glavne 5 ikonice */}
          {mainNavigation.map(item => (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'flex flex-col items-center justify-center p-2 min-w-[52px] rounded-xl transition-all duration-300 ease-in-out transform',
                  isActive(item.href)
                    ? 'text-white bg-gradient-to-br from-[#7F5BFE] via-[#F35BF6] to-[#f04a13] shadow-lg shadow-purple-500/25 scale-105'
                    : 'text-muted-foreground hover:text-foreground hover:bg-gradient-to-br hover:from-accent/80 hover:to-accent/60 hover:scale-105 hover:shadow-md'
                )}
              >
                <item.icon
                  className={cn(
                    'h-5 w-5 mb-1 transition-all duration-300',
                    isActive(item.href) ? 'drop-shadow-sm' : ''
                  )}
                />
                <span
                  className={cn(
                    'text-xs font-medium transition-all duration-300',
                    isActive(item.href) ? 'font-semibold drop-shadow-sm' : ''
                  )}
                >
                  {item.label}
                </span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
}
