'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { BackButton } from '@/components/ui/back-button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bell,
  ChevronDown,
  User,
  Settings,
  DollarSign,
  LogOut,
  CheckCircle,
  Send,
  FileText,
  Users,
  Check,
  CheckCheck,
  ArrowLeft,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount,
  type Notification,
} from '@/lib/notifications';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import { useEffect } from 'react';
import { getDisplayName, getInitials } from '@/lib/utils';

interface MobileTopNavbarProps {
  userType: 'influencer' | 'business';
  profile: any;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

interface ProfileMenuItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

export function MobileTopNavbar({
  userType,
  profile,
  showBackButton = false,
  onBackClick,
}: MobileTopNavbarProps) {
  const { user, signOut } = useAuth();
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Profile dropdown items for business users
  const businessProfileItems: ProfileMenuItem[] = [
    {
      name: 'Postavke računa',
      href: '/dashboard/biznis/account',
      icon: Settings,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Moj profil',
      href: '/dashboard/biznis/profile',
      icon: User,
      description: 'Javni profil firme',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Profile dropdown items for influencer users
  const influencerProfileItems: ProfileMenuItem[] = [
    {
      name: 'Postavke računa',
      href: '/dashboard/influencer/account',
      icon: Settings,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Moj profil',
      href: '/dashboard/influencer/profile',
      icon: User,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: DollarSign,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const profileItems =
    userType === 'influencer' ? influencerProfileItems : businessProfileItems;

  useEffect(() => {
    if (user) {
      loadNotifications();
      loadUnreadCount();
    }
  }, [user]);

  const loadNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const { data, error } = await getUserNotifications(user.id, 20);
      if (error) {
        console.error('Error loading notifications:', error);
      } else {
        setNotifications(data || []);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUnreadCount = async () => {
    if (!user) return;

    try {
      const { count, error } = await getUnreadNotificationCount(user.id);
      if (error) {
        console.error('Error loading unread count:', error);
      } else {
        setUnreadCount(count);
      }
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const { error } = await markNotificationAsRead(notificationId);
      if (error) {
        toast.error('Greška pri označavanju notifikacije');
      } else {
        setNotifications(prev =>
          prev.map(n => (n.id === notificationId ? { ...n, read: true } : n))
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Greška pri označavanju notifikacije');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      const { error } = await markAllNotificationsAsRead(user.id);
      if (error) {
        toast.error('Greška pri označavanju notifikacija');
      } else {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        setUnreadCount(0);
        toast.success('Sve notifikacije označene kao pročitane');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Greška pri označavanju notifikacija');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
        return <FileText className="h-4 w-4" />;
      case 'campaign_application':
      case 'campaign_accepted':
      case 'campaign_rejected':
        return <FileText className="h-4 w-4" />;
      case 'job_completion_submitted':
      case 'job_completion_approved':
      case 'job_completion_rejected':
        return <CheckCheck className="h-4 w-4" />;
      case 'message_received':
        return <Bell className="h-4 w-4" />;
      case 'payment_received':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationLink = (notification: Notification) => {
    switch (notification.type) {
      case 'offer_received':
      case 'offer_accepted':
      case 'offer_rejected':
        if (notification.data.offer_id) {
          return `/dashboard/influencer/offers/${notification.data.offer_id}`;
        }
        return '/dashboard/influencer/offers';
      case 'job_completion_submitted':
        if (notification.data.direct_offer_id) {
          return `/dashboard/biznis/offers/${notification.data.direct_offer_id}`;
        }
        return '/dashboard/biznis/offers';
      case 'job_completion_approved':
      case 'job_completion_rejected':
        if (notification.data.job_completion_id) {
          return '/dashboard/job-completions';
        }
        return '/dashboard';
      case 'message_received':
        if (notification.data.conversation_id) {
          return `/dashboard/chat/${notification.data.conversation_id}`;
        }
        return '/dashboard/chat';
      default:
        return '/dashboard';
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-purple-500/20 nav-bar-gradient-subtle backdrop-blur-md supports-[backdrop-filter]:bg-background/60 md:hidden shadow-lg shadow-purple-500/10">
      <div className="flex h-14 items-center justify-between px-4">
        {/* Logo or Back Button */}
        {showBackButton ? (
          <BackButton onClick={onBackClick || (() => router.back())} />
        ) : (
          <Link href="/" className="flex items-center space-x-2 group">
            <Image
              src="/images/influexus_logo.webp"
              alt="Influexus Logo"
              width={28}
              height={28}
              className="rounded-lg transition-transform duration-300 group-hover:scale-110"
            />
            <h1 className="text-lg font-bold gradient-text transition-all duration-300 group-hover:drop-shadow-sm">
              Influexus
            </h1>
          </Link>
        )}

        {/* Right side - Notifications & Profile */}
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative p-2">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                  >
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel className="flex items-center justify-between p-4">
                <h3 className="font-semibold">Notifikacije</h3>
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs"
                  >
                    <CheckCheck className="h-3 w-3 mr-1" />
                    Označi sve
                  </Button>
                )}
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              <ScrollArea className="h-96">
                {isLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  </div>
                ) : notifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-center">
                    <Bell className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Nema novih notifikacija
                    </p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {notifications.map(notification => (
                      <div
                        key={notification.id}
                        className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${
                          notification.read
                            ? 'border-transparent'
                            : 'border-primary'
                        }`}
                      >
                        <Link
                          href={getNotificationLink(notification)}
                          onClick={() => {
                            if (!notification.read) {
                              handleMarkAsRead(notification.id);
                            }
                          }}
                          className="block"
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-0.5">
                              {getNotificationIcon(notification.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <h4
                                  className={`text-sm font-medium ${
                                    notification.read
                                      ? 'text-muted-foreground'
                                      : 'text-foreground'
                                  }`}
                                >
                                  {notification.title}
                                </h4>
                                {!notification.read && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={e => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleMarkAsRead(notification.id);
                                    }}
                                    className="h-6 w-6 p-0 ml-2"
                                  >
                                    <Check className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                              <p
                                className={`text-xs mt-1 ${
                                  notification.read
                                    ? 'text-muted-foreground'
                                    : 'text-muted-foreground'
                                }`}
                              >
                                {notification.message}
                              </p>
                              <p className="text-xs text-muted-foreground mt-1">
                                {formatDistanceToNow(
                                  new Date(notification.created_at),
                                  {
                                    addSuffix: true,
                                    locale: hr,
                                  }
                                )}
                              </p>
                            </div>
                          </div>
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {notifications.length > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full"
                      asChild
                    >
                      <Link href="/dashboard/notifications">
                        Pogledaj sve notifikacije
                      </Link>
                    </Button>
                  </div>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Profile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center space-x-1 px-2 py-1 rounded-xl hover:bg-gradient-to-r hover:from-accent/80 hover:to-accent/60 transition-all duration-300 group"
              >
                <Avatar className="h-7 w-7 ring-1 ring-purple-500/20 group-hover:ring-purple-500/40 transition-all duration-300">
                  <AvatarImage src={profile?.avatar_url} alt="Profile" />
                  <AvatarFallback className="bg-gradient-to-br from-[#7F5BFE] to-[#F35BF6] text-white font-semibold text-xs">
                    {getInitials(
                      getDisplayName(profile) !== 'Ime i prezime skriveno'
                        ? getDisplayName(profile)
                        : profile?.username
                    )}
                  </AvatarFallback>
                </Avatar>
                <ChevronDown className="h-3 w-3 text-muted-foreground group-hover:text-foreground transition-colors duration-300" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-64 bg-card/95 backdrop-blur-md border border-purple-500/20 shadow-xl shadow-purple-500/10 rounded-xl overflow-hidden"
            >
              <div className="relative p-3 bg-gradient-to-br from-[#7F5BFE]/8 via-[#F35BF6]/4 to-[#f04a13]/8 border-b border-purple-500/10">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-50"></div>
                <div className="relative flex items-center space-x-3">
                  <Avatar className="h-10 w-10 ring-2 ring-purple-500/30 ring-offset-1 ring-offset-background">
                    <AvatarImage src={profile?.avatar_url} alt="Profile" />
                    <AvatarFallback className="bg-gradient-to-br from-[#7F5BFE] to-[#F35BF6] text-white font-bold">
                      {getInitials(
                        getDisplayName(profile) !== 'Ime i prezime skriveno'
                          ? getDisplayName(profile)
                          : profile?.username
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-bold text-foreground">
                      {getDisplayName(profile) !== 'Ime i prezime skriveno'
                        ? getDisplayName(profile)
                        : profile?.username}
                    </p>
                    <p className="text-xs text-muted-foreground font-medium">
                      @{profile?.username}
                    </p>
                  </div>
                </div>
              </div>
              <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-purple-500/20 to-transparent" />
              <div className="p-1 space-y-1">
                {profileItems.map(item => (
                  <DropdownMenuItem
                    key={item.name}
                    asChild
                    className="cursor-pointer hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/5 transition-all duration-300 rounded-lg border border-transparent hover:border-purple-500/20 group"
                  >
                    <Link href={item.href} className="flex items-center p-2">
                      <div className="flex items-center justify-center w-7 h-7 rounded-lg bg-gradient-to-br from-purple-500/10 to-pink-500/10 mr-2 group-hover:from-purple-500/20 group-hover:to-pink-500/20 transition-all duration-300">
                        <item.icon className="h-3.5 w-3.5 text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <span className="text-sm font-medium group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors duration-300">
                        {item.name}
                      </span>
                    </Link>
                  </DropdownMenuItem>
                ))}
              </div>
              <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-red-500/20 to-transparent" />
              <div className="p-1">
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="cursor-pointer text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 dark:hover:from-red-950/30 dark:hover:to-red-900/30 hover:text-red-700 dark:hover:text-red-400 transition-all duration-300 rounded-lg border border-transparent hover:border-red-500/20 group"
                >
                  <div className="flex items-center justify-center w-7 h-7 rounded-lg bg-red-500/10 mr-2 group-hover:bg-red-500/20 transition-all duration-300">
                    <LogOut className="h-3.5 w-3.5 group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <span className="text-sm font-medium">Odjavi se</span>
                </DropdownMenuItem>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
