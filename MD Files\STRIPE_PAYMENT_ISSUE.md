# Stripe Payment Issue - RLS Problem

## Problem
- **Applications**: `Error: Application not found or not accessible`
- **Offers**: `Error: Offer not found or not accessible`

## Root Cause
RLS (Row Level Security) policies u Supabase blokira pristup kada Stripe API endpointi pokušaju da čitaju:
- `campaign_applications` tabelu
- `direct_offers` tabelu

## Technical Details
API endpointi koriste:
- `/api/stripe/create-application-payment`  
- `/api/stripe/create-offer-payment`

<PERSON>ba pozivaju `.single()` query koji vraća "JSON object requested, multiple (or no) rows returned"

## Quick Fix Needed
Proveriti RLS policies za:
1. `campaign_applications` tabela
2. `direct_offers` tabela  
3. Možda dodati service role key za API endpointe umjesto user context

## Status
- TypeScript greške cleaned up ✅
- Stripe integration ready ✅  
- RLS blocking database access ❌

## Next Steps
1. Check RLS policies in Supabase
2. Test payments after RLS fix