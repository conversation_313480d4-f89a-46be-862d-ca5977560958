# 🔧 ANALIZA FUNKCIJA - INFLUENCER PLATFORMA
**Datum:** 05.08.2025  
**Analizirano:** Sve funkcije u src/lib i src/app direktorijima

---

## 📋 PREGLED FUNKCIJA PO FAJLOVIMA

### 🗂️ **src/lib/profiles.ts** (19 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `getProfile(userId)` | Dohvaća profil korisnika | ✅ Aktivna | <PERSON><PERSON><PERSON> |
| `createProfile(profileData)` | Kreira novi profil | ✅ Aktivna | Registracija |
| `getOrCreateProfile(userId)` | Dohvaća ili kreira profil | ✅ Aktivna | Auth flow |
| `updateProfile(userId, updates)` | Ažurira profil | ✅ Aktivna | Uređivanje profila |
| `upsertProfile(userId, updates)` | **DUPLIKAT** - upsert logika | ⚠️ Redundantna | Rijetko k<PERSON> |
| `getPublicInfluencerProfile(username)` | Javni profil influencera | ✅ Aktivna | Javne stranice |
| `getInfluencers(filters)` | **ZASTARJELA** - lista influencera | 🔴 Zamijenjena | Marketplace |
| `getBusiness(userId)` | Dohvaća biznis profil | ✅ Aktivna | Biznis dashboard |
| `createBusiness(businessData)` | Kreira biznis profil | ✅ Aktivna | Registracija |
| `updateBusiness(userId, updates)` | Ažurira biznis profil | ✅ Aktivna | Uređivanje |
| `getInfluencer(userId)` | Dohvaća influencer profil | ✅ Aktivna | Dashboard |
| `createInfluencer(influencerData)` | Kreira influencer profil | ✅ Aktivna | Onboarding |
| `updateInfluencer(userId, updates)` | Ažurira influencer profil | ✅ Aktivna | Uređivanje |
| `getInfluencerCategories(influencerId)` | Kategorije influencera | ✅ Aktivna | Profil |
| `addInfluencerCategories(influencerId, categoryIds)` | Dodaje kategorije | ✅ Aktivna | Onboarding |
| `removeInfluencerCategories(influencerId, categoryIds)` | Uklanja kategorije | ✅ Aktivna | Uređivanje |
| `uploadAvatar(userId, file)` | Upload avatar slike | ✅ Aktivna | Profil |
| `getInfluencerPlatforms(influencerId)` | Platforme influencera | ✅ Aktivna | Profil |
| `getInfluencerPricing(influencerId)` | Pricing paketi | ✅ Aktivna | Profil |

### 🗂️ **src/lib/campaigns.ts** (25 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `createCampaign(campaign)` | Kreira novu kampanju | ✅ Aktivna | Biznis dashboard |
| `getCampaign(id)` | Dohvaća kampanju po ID | ✅ Aktivna | Detalji kampanje |
| `getCampaignWithDetails(id)` | **DUPLIKAT** - kampanja sa detaljima | ⚠️ Slična | Javne stranice |
| `deleteCampaign(id)` | Briše kampanju | ✅ Aktivna | Biznis dashboard |
| `updateCampaign(campaignId, data)` | Ažurira kampanju | ✅ Aktivna | Uređivanje |
| `getCampaignForEdit(campaignId)` | **DUPLIKAT** - kampanja za edit | ⚠️ Slična | Edit forma |
| `searchCampaigns(filters)` | Pretraga kampanja | ✅ Aktivna | Marketplace |
| `getBusinessCampaigns(businessId)` | Kampanje biznisa | ✅ Aktivna | Dashboard |
| `applyToCampaign(applicationData)` | Aplikacija na kampanju | ✅ Aktivna | Influencer |
| `getCampaignApplication(applicationId)` | Detalji aplikacije | ✅ Aktivna | Dashboard |
| `updateApplicationStatus(applicationId, status)` | Ažurira status aplikacije | ✅ Aktivna | Biznis review |
| `getInfluencerApplications(influencerId)` | Aplikacije influencera | ✅ Aktivna | Dashboard |
| `hasInfluencerApplied(campaignId, influencerId)` | Provjera aplikacije | ✅ Aktivna | UI logika |
| `addCampaignPlatforms(campaignId, platforms)` | Dodaje platforme | ✅ Aktivna | Kreiranje |
| `addCampaignCategories(campaignId, categories)` | Dodaje kategorije | ✅ Aktivna | Kreiranje |
| `getCampaignPlatforms(campaignId)` | Platforme kampanje | ✅ Aktivna | Detalji |
| `getCampaignCategories(campaignId)` | Kategorije kampanje | ✅ Aktivna | Detalji |
| `getApplicationsForCampaign(campaignId)` | Aplikacije za kampanju | ✅ Aktivna | Biznis review |
| `acceptApplication(applicationId, feedback)` | Prihvaća aplikaciju | ✅ Aktivna | Biznis |
| `rejectApplication(applicationId, reason)` | Odbija aplikaciju | ✅ Aktivna | Biznis |
| `getBusinessApplications(businessId)` | Aplikacije biznisa | ✅ Aktivna | Dashboard |
| `getCampaignStats(campaignId)` | Statistike kampanje | ✅ Aktivna | Analytics |
| `getInfluencerCampaignStats(influencerId)` | Stats influencera | ✅ Aktivna | Dashboard |
| `markCampaignAsCompleted(campaignId)` | Označava kao završenu | ✅ Aktivna | Workflow |
| `getCampaignsByStatus(businessId, status)` | Kampanje po statusu | ✅ Aktivna | Filtriranje |

### 🗂️ **src/lib/chat.ts** (12 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `getOrCreateChatRoomForApplication(applicationId)` | Chat za aplikaciju | ✅ Aktivna | Chat sistem |
| `getOrCreateChatRoomForOffer(offerId)` | Chat za ponudu | ✅ Aktivna | Chat sistem |
| `getChatRooms(userId, userType)` | Lista chat soba | ✅ Aktivna | Chat dashboard |
| `getChatMessages(roomId, limit, offset)` | Poruke u sobi | ✅ Aktivna | Chat UI |
| `sendChatMessage(roomId, messageText, files...)` | Šalje poruku | ✅ Aktivna | Chat UI |
| `markMessagesAsRead(roomId, userId)` | Označava kao pročitano | ✅ Aktivna | Chat UI |
| `getChatRoom(roomId)` | Detalji chat sobe | ✅ Aktivna | Chat UI |
| `enableChatForApplication(applicationId, approvals)` | Omogućava chat | ✅ Aktivna | Workflow |
| `enableChatForOffer(offerId, approvals)` | Omogućava chat | ✅ Aktivna | Workflow |
| `addChatParticipant(roomId, userId, userType)` | Dodaje učesnika | ✅ Aktivna | Chat setup |
| `removeChatParticipant(roomId, userId)` | Uklanja učesnika | ✅ Aktivna | Chat admin |
| `getChatParticipants(roomId)` | Lista učesnika | ✅ Aktivna | Chat UI |

### 🗂️ **src/lib/chat-permissions.ts** (6 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `upsertOfferChatPermission(businessId, influencerId, offerId, approvals)` | **DUPLIKAT A** | ⚠️ Slična | Chat dozvole |
| `upsertApplicationChatPermission(businessId, influencerId, applicationId, approvals)` | **DUPLIKAT B** | ⚠️ Slična | Chat dozvole |
| `isChatEnabled(businessId, influencerId, offerId?, applicationId?)` | Provjera chat dozvole | ✅ Aktivna | UI logika |
| `getChatPermission(businessId, influencerId, offerId?, applicationId?)` | Detalji dozvole | ✅ Aktivna | Chat UI |
| `approveChatPermission(permissionId, userType)` | Odobrava chat | ✅ Aktivna | Workflow |
| `revokeChatPermission(permissionId)` | Opoziva dozvolu | ✅ Aktivna | Admin |

### 🗂️ **src/lib/marketplace.ts** (8 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `searchInfluencers(filters)` | Pretraga influencera | ✅ Aktivna | Marketplace |
| `getInfluencerProfile(username)` | **DUPLIKAT** - javni profil | ⚠️ Slična | Javne stranice |
| `getCategories()` | **DUPLIKAT** - kategorije | ⚠️ Slična | Filteri |
| `getPlatforms()` | **MOCK** - platforme | 🔴 Mock data | Filteri |
| `getContentTypes()` | **DUPLIKAT** - tipovi sadržaja | ⚠️ Slična | Filteri |
| `getLocations()` | **MOCK** - lokacije | 🔴 Mock data | Filteri |
| `getInfluencersByCategory(categoryId)` | Influenceri po kategoriji | ✅ Aktivna | Kategorije |
| `getInfluencersByPlatform(platformId)` | Influenceri po platformi | ✅ Aktivna | Platforme |

### 🗂️ **src/lib/pricing-packages.ts** (12 funkcija)

| Funkcija | Svrha | Status | Korišćenje |
|----------|-------|--------|------------|
| `generatePackageName(quantity, platform, contentType, duration)` | **DUPLIKAT** - SQL funkcija | ⚠️ Duplikat | Kreiranje paketa |
| `getPlatforms()` | **DUPLIKAT** - platforme | ⚠️ Slična | Paketi |
| `getContentTypes()` | **DUPLIKAT** - tipovi sadržaja | ⚠️ Slična | Paketi |
| `getContentTypesByPlatform(platformId)` | Tipovi po platformi | ✅ Aktivna | UI |
| `getPlatformsWithContentTypes()` | Platforme sa tipovima | ✅ Aktivna | UI |
| `getInfluencerPackages(influencerId)` | **DUPLIKAT** - paketi influencera | ⚠️ Slična | Dashboard |
| `createPricingPackage(influencerId, packageData)` | Kreira paket | ✅ Aktivna | Onboarding |
| `updatePricingPackage(packageId, updates)` | Ažurira paket | ✅ Aktivna | Uređivanje |
| `deletePricingPackage(packageId)` | Briše paket | ✅ Aktivna | Management |
| `togglePackageAvailability(packageId, isAvailable)` | Toggle dostupnost | ✅ Aktivna | Management |
| `getInfluencerPricingForMarketplace(influencerId)` | **DUPLIKAT** - marketplace pricing | ⚠️ Slična | Marketplace |
| `bulkUpdatePackages(influencerId, packages)` | Bulk ažuriranje | ✅ Aktivna | Management |

---

## 🚨 IDENTIFIKOVANI PROBLEMI

### 1. **DUPLIKATI FUNKCIJA** ⚠️

#### A) **Chat Permissions - ISTI KOD**
```typescript
// chat-permissions.ts
upsertOfferChatPermission(businessId, influencerId, offerId, ...)
upsertApplicationChatPermission(businessId, influencerId, applicationId, ...)
```
**Problem:** Skoro identične funkcije, razlika samo u jednom polju

#### B) **Profile Functions - REDUNDANTNE**
```typescript
// profiles.ts
updateProfile(userId, updates)  // Osnovni update
upsertProfile(userId, updates)  // Poziva updateProfile interno
```
**Problem:** `upsertProfile` samo poziva `updateProfile`

#### C) **Campaign Details - SLIČNE**
```typescript
// campaigns.ts
getCampaign(id)              // Osnovna kampanja
getCampaignWithDetails(id)   // Kampanja sa business detaljima
getCampaignForEdit(id)       // Kampanja za editiranje
```
**Problem:** 3 funkcije za slične stvari

#### D) **Pricing Packages - DUPLIKATI**
```typescript
// profiles.ts
getInfluencerPricing(influencerId)

// pricing-packages.ts  
getInfluencerPackages(influencerId)
getInfluencerPricingForMarketplace(influencerId)
```
**Problem:** 3 funkcije za iste podatke

#### E) **Platform/Category Functions - RAŠTRKANE**
```typescript
// marketplace.ts
getPlatforms()     // Mock data
getCategories()    // Poziva categories.ts

// pricing-packages.ts
getPlatforms()     // Prava baza
getContentTypes()  // Prava baza

// categories.ts
getCategories()    // Prava baza
```
**Problem:** Iste funkcije u različitim fajlovima

### 2. **ZASTARJELE FUNKCIJE** 🔴

#### A) **Mock Data Functions**
```typescript
// marketplace.ts
getPlatforms()    // Vraća mock podatke umjesto baze
getLocations()    // Vraća mock podatke
```

#### B) **Zamijenjena Logika**
```typescript
// profiles.ts
getInfluencers(filters)  // Zamijenjena sa searchInfluencers()
```

### 3. **NELOGIČNA ORGANIZACIJA** 🤔

#### A) **Funkcije u pogrešnim fajlovima**
- `generatePackageName()` - postoji i u SQL i TypeScript
- Platform funkcije raštrkane po 3 fajla
- Category funkcije u 2 fajla

---

## 💡 PREPORUKE ZA REFAKTORISANJE

### 🔧 **PRIORITET 1: UKLONITI DUPLIKATE**

#### 1. **Ujediniti Chat Permissions**
```typescript
// Umjesto 2 funkcije:
export async function upsertChatPermission(
  businessId: string,
  influencerId: string,
  referenceId: string,
  referenceType: 'offer' | 'application',
  businessApproved: boolean = false,
  influencerApproved: boolean = false
)
```

#### 2. **Pojednostaviti Profile Functions**
```typescript
// Obrisati upsertProfile, zadržati samo updateProfile
// ili obrnuto - zadržati samo upsert logiku
```

#### 3. **Konsolidovati Campaign Functions**
```typescript
// Spojiti u jednu funkciju sa opcionalnim parametrima:
getCampaign(id: string, includeDetails?: boolean, forEdit?: boolean)
```

#### 4. **Centralizovati Platform/Category Functions**
```typescript
// Kreirati src/lib/platform-data.ts
export { getPlatforms, getCategories, getContentTypes }

// Ukloniti iz ostalih fajlova
```

### 🔧 **PRIORITET 2: ORGANIZACIJA**

#### 1. **Kreirati nove fajlove:**
- `src/lib/platform-data.ts` - sve platform/category funkcije
- `src/lib/package-management.ts` - sve pricing funkcije
- `src/lib/application-workflow.ts` - workflow funkcije

#### 2. **Reorganizovati postojeće:**
- `profiles.ts` - samo osnovni CRUD
- `campaigns.ts` - samo campaign CRUD
- `chat.ts` - samo chat funkcionalnost

### 🔧 **PRIORITET 3: ČIŠĆENJE**

#### 1. **Obrisati zastarjele:**
```typescript
// profiles.ts
getInfluencers()  // Zamijenjena sa marketplace.searchInfluencers()

// marketplace.ts  
getPlatforms()    // Mock data
getLocations()    // Mock data
```

#### 2. **Ukloniti SQL duplikat:**
```sql
-- Obrisati generate_package_name iz baze
-- Koristiti samo TypeScript verziju
```

---

## 📊 STATISTIKE

- **Ukupno funkcija:** ~95
- **Aktivnih:** 78 (82%)
- **Duplikata:** 12 (13%)
- **Zastarjelih:** 5 (5%)
- **Fajlova za refaktorisanje:** 6

**Preporučeno smanjenje:** 15-20 funkcija (20% manje koda)
