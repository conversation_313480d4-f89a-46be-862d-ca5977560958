import { supabase } from './supabase';
import {
  notify<PERSON><PERSON><PERSON><PERSON>eived,
  notify<PERSON>fferAccepted,
  notify<PERSON><PERSON>Rejected,
  notifyPayment<PERSON>equiredOfferAccepted,
  notify<PERSON><PERSON>r<PERSON><PERSON>eived,
  notifyPaymentRequiredOrderAccepted,
} from './notifications';
import { upsertOfferChatPermission } from './chat-permissions';

export interface DirectOfferInsert {
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  deadline?: string | null;
  requirements?: string | null;
  offer_type?: 'custom' | 'package_order';
  package_id?: number | null;
  business_message?: string | null;
}

export interface DirectOffer {
  id: string;
  business_id: string;
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  deadline?: string | null;
  requirements?: string | null;
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  offer_type: 'custom' | 'package_order';
  package_id?: number | null;
  business_message?: string | null;
  influencer_response?: string | null;
  created_at: string;
  updated_at: string;
  responded_at?: string | null;
  accepted_at?: string | null;
  rejected_at?: string | null;
}

export interface DirectOfferWithDetails extends DirectOffer {
  businesses?: {
    id: string;
    company_name: string;
    industry?: string;
    categories?: {
      id: number;
      name: string;
      icon?: string;
    }[];
    profiles: {
      avatar_url?: string;
      username?: string;
    };
  };
  influencer: {
    id: string;
    full_name: string | null;
    public_display_name: string | null;
    username: string;
    avatar_url?: string | null;
  };
}

// Interface types for database operations and results
interface BusinessCategory {
  business_id: string;
  categories: {
    id: number;
    name: string;
    icon: string | null;
  };
}

interface OfferUpdateData {
  status: string;
  updated_at: string;
  responded_at: string;
  influencer_response?: string;
  accepted_at?: string;
  rejected_at?: string;
}

interface PaymentInfo {
  campaign_id?: string;
  offer_id?: string;
  amount: number;
  currency: string;
  description?: string;
  [key: string]: unknown;
}

// Kreiranje direktne ponude
export async function createDirectOffer(offerData: DirectOfferInsert) {
  try {
    // Prvo dobijamo trenutnog korisnika
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      throw new Error('Korisnik nije prijavljen');
    }

    // Proveravamo da li je korisnik business tip
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, user_type')
      .eq('id', user.user.id)
      .single();

    if (profileError || !profile) {
      throw new Error('Profil nije pronađen');
    }

    if (profile.user_type !== 'business') {
      throw new Error('Samo business korisnici mogu kreirati ponude');
    }

    console.log('Creating direct offer with data:', {
      business_id: profile.id,
      ...offerData,
    });

    const { data, error } = await supabase
      .from('direct_offers')
      .insert({
        business_id: profile.id,
        offer_type: 'custom',
        ...offerData,
      })
      .select('*')
      .single();

    console.log('Direct offer creation result:', { data, error });

    if (error) {
      console.error('Direct offer creation error:', error);
      throw new Error(`Greška pri kreiranju ponude: ${error.message}`);
    }

    // Kreiraj notifikaciju za influencera
    if (data) {
      try {
        // Get business and influencer data separately for notification
        const { data: businessProfile } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', profile.id)
          .single();

        const { data: influencerProfile } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.influencer_id)
          .single();

        if (businessProfile && influencerProfile) {
          await notifyOfferReceived(
            influencerProfile.id,
            businessProfile.full_name || 'Business',
            data.title,
            data.id
          );
        }
      } catch (notificationError) {
        console.error('Error creating notification:', notificationError);
        // Ne prekidamo proces ako notifikacija ne uspije
      }

      // Kreiraj chat permission - business NEĆE biti approved dok ne plati
      try {
        await upsertOfferChatPermission(
          profile.id,
          data.influencer_id,
          data.id,
          false, // business_approved - NEĆE biti approved dok ne plati nakon što influencer prihvati
          false // influencer_approved - čeka se odgovor influencera
        );
      } catch (chatError) {
        console.error('Error creating chat permission:', chatError);
        // Ne prekidamo proces ako chat permission ne uspije
      }
    }

    return { data, error };
  } catch (error) {
    console.error('Error creating direct offer:', error);
    return { data: null, error };
  }
}

// Kreiranje narudžbe paketa
export async function createPackageOrder(packageData: {
  influencer_id: string;
  package_id: number;
  package_name: string;
  price: number;
  platform_name: string;
  content_type_name: string;
  requirements?: string;
  message?: string;
}) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Kreiraj narudžbu kao direktnu ponudu sa tipom 'package_order'
    const offerData: DirectOfferInsert = {
      influencer_id: packageData.influencer_id,
      title: `Narudžba: ${packageData.package_name}`,
      description: `Narudžba paketa "${packageData.package_name}" na ${packageData.platform_name} platformi`,
      budget: packageData.price,
      content_types: [packageData.content_type_name],
      platforms: [packageData.platform_name],
      offer_type: 'package_order',
      package_id: packageData.package_id,
      requirements:
        packageData.requirements ||
        `Automatski kreirana narudžba za paket: ${packageData.package_name}`,
      business_message:
        packageData.message || `Narudžba za paket: ${packageData.package_name}`,
    };

    const { data, error } = await supabase
      .from('direct_offers')
      .insert({
        business_id: user.user.id,
        ...offerData,
      })
      .select()
      .single();

    if (error) throw error;

    // Dobij business profil za notifikaciju
    const { data: businessProfile } = await supabase
      .from('profiles')
      .select('username')
      .eq('id', user.user.id)
      .single();

    // Dobij informacije o package-u iz baze
    const { data: packageDetails } = await supabase
      .from('influencer_platform_pricing')
      .select('auto_generated_name, price, currency')
      .eq('id', packageData.package_id)
      .single();

    // Pošalji notifikaciju influenceru o novoj narudžbi
    await notifyOrderReceived(
      data.influencer_id,
      businessProfile?.username || 'Business',
      packageDetails?.auto_generated_name || packageData.package_name,
      data.id,
      packageDetails?.price || packageData.price,
      packageDetails?.currency || 'EUR'
    );

    // Kreiraj chat permission - business NEĆE biti approved dok ne plati
    await upsertOfferChatPermission(
      user.user.id,
      data.influencer_id,
      data.id,
      false, // business_approved - NEĆE biti approved dok ne plati nakon što influencer prihvati
      false // influencer_approved - čeka se odgovor influencera
    );

    return { data, error: null };
  } catch (error) {
    console.error('Error creating package order:', error);
    return { data: null, error };
  }
}

// Dobijanje ponuda za biznis
export async function getBusinessOffers(status?: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Prvo dobijamo ponude za ovaj biznis
    let offersQuery = supabase
      .from('direct_offers')
      .select('*')
      .eq('business_id', user.user.id)
      .order('created_at', { ascending: false });

    if (status) {
      offersQuery = offersQuery.eq('status', status);
    }

    const { data: offers, error: offersError } = await offersQuery;
    if (offersError) throw offersError;

    if (!offers || offers.length === 0) {
      return { data: [], error: null };
    }

    // Zatim dobijamo profile influencera
    const influencerIds = offers.map(offer => offer.influencer_id);
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, full_name, public_display_name, avatar_url')
      .in('id', influencerIds);

    if (profilesError) throw profilesError;

    // Kombinujemo podatke
    const offersWithDetails = offers.map(offer => ({
      ...offer,
      influencer: profiles?.find(p => p.id === offer.influencer_id) || {
        id: offer.influencer_id,
        username: 'Unknown',
        full_name: 'Unknown User',
        avatar_url: null,
      },
    }));

    return { data: offersWithDetails as DirectOfferWithDetails[], error: null };
  } catch (error) {
    console.error('Error fetching business offers:', error);
    return { data: null, error };
  }
}

// Dobijanje ponuda za influencera
export async function getInfluencerOffers(status?: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Prvo dobijamo influencer_id iz profiles tabele
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.user.id)
      .eq('user_type', 'influencer')
      .single();

    if (profileError || !profileData) {
      console.log('Influencer profile not found for user:', user.user.id);
      return { data: [], error: null };
    }

    let query = supabase
      .from('direct_offers')
      .select(
        `
        *,
        businesses!inner(
          id,
          company_name,
          profiles!inner(
            avatar_url,
            username
          )
        )
      `
      )
      .eq('influencer_id', profileData.id)
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) throw error;

    // Dodaj kategorije za svaki business
    if (data && data.length > 0) {
      const businessIds = [...new Set(data.map(offer => offer.business_id))];

      // Get categories for all businesses
      const { data: businessCategories } = await supabase
        .from('business_target_categories')
        .select(
          `
          business_id,
          categories (
            id,
            name,
            icon
          )
        `
        )
        .in('business_id', businessIds);

      // Map categories to businesses
      const businessCategoriesMap: { [key: string]: BusinessCategory['categories'][] } = {};
      if (businessCategories) {
        businessCategories.forEach(bc => {
          if (!businessCategoriesMap[bc.business_id]) {
            businessCategoriesMap[bc.business_id] = [];
          }
          businessCategoriesMap[bc.business_id].push(bc.categories);
        });
      }

      // Add categories to each offer
      const offersWithCategories = data.map(offer => ({
        ...offer,
        businesses: {
          ...offer.businesses,
          categories: businessCategoriesMap[offer.business_id] || [],
        },
      }));

      return {
        data: offersWithCategories as DirectOfferWithDetails[],
        error: null,
      };
    }

    return { data: data as DirectOfferWithDetails[], error: null };
  } catch (error) {
    console.error('Error fetching influencer offers:', error);
    return { data: null, error };
  }
}

// Ažuriranje statusa ponude
export async function updateOfferStatus(
  offerId: string,
  status: 'accepted' | 'rejected',
  influencerResponse?: string
) {
  try {
    const updateData: OfferUpdateData = {
      status,
      updated_at: new Date().toISOString(),
      responded_at: new Date().toISOString(),
    };

    // Dodaj influencer response ako je prosleđen
    if (influencerResponse) {
      updateData.influencer_response = influencerResponse;
    }

    // Dodaj specifične timestamp-ove na osnovu statusa
    if (status === 'accepted') {
      updateData.accepted_at = new Date().toISOString();
    } else if (status === 'rejected') {
      updateData.rejected_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('direct_offers')
      .update(updateData)
      .eq('id', offerId)
      .select('*')
      .single();

    // Kreiraj notifikaciju za biznis i chat dozvole
    if (data && !error) {
      try {
        // Dobij informacije o profilima za notifikacije
        const { data: businessProfile } = await supabase
          .from('profiles')
          .select('id, username')
          .eq('id', data.business_id)
          .single();

        const { data: influencerProfile } = await supabase
          .from('profiles')
          .select('id, username')
          .eq('id', data.influencer_id)
          .single();

        if (status === 'accepted') {
          if (businessProfile && influencerProfile) {
            if (data.offer_type === 'package_order') {
              // Notifikacije za package order
              await notifyOfferAccepted(
                businessProfile.id,
                influencerProfile.username,
                data.title,
                data.id
              );

              // Dobij package podatke za notifikaciju
              const { data: packageData } = await supabase
                .from('influencer_platform_pricing')
                .select('auto_generated_name, price, currency')
                .eq('id', data.package_id)
                .single();

              await notifyPaymentRequiredOrderAccepted(
                businessProfile.id,
                influencerProfile.username,
                packageData?.auto_generated_name || data.title,
                data.id,
                packageData?.price || data.budget,
                packageData?.currency || 'EUR'
              );
            } else {
              // Notifikacije za direktne ponude
              await notifyOfferAccepted(
                businessProfile.id,
                influencerProfile.username,
                data.title,
                data.id
              );

              await notifyPaymentRequiredOfferAccepted(
                businessProfile.id,
                influencerProfile.username,
                data.title,
                data.id,
                data.budget,
                'EUR'
              );
            }
          }

          // Ažuriraj chat dozvolu kada influencer prihvati ponudu
          // Influencer je odobrio, ali business_approved će biti true tek nakon plaćanja
          await upsertOfferChatPermission(
            data.business_id,
            data.influencer_id,
            data.id,
            false, // business_approved - NEĆE biti true dok business ne plati
            true // influencer_approved - influencer je sada odobrio prihvatanjem
          );
        } else if (status === 'rejected') {
          if (businessProfile && influencerProfile) {
            await notifyOfferRejected(
              businessProfile.id,
              influencerProfile.username,
              data.title,
              data.id
            );
          }
        }
      } catch (notificationError) {
        console.error(
          'Error creating notification or chat permission:',
          notificationError
        );
        // Ne prekidamo proces ako notifikacija ne uspije
      }
    }

    return { data, error };
  } catch (error) {
    console.error('Error updating offer status:', error);
    return { data: null, error };
  }
}

// Dobijanje pojedinačne ponude sa detaljima
export async function getDirectOffer(offerId: string) {
  try {
    // Prvo dobijamo osnovne podatke o ponudi
    const { data: offer, error: offerError } = await supabase
      .from('direct_offers')
      .select('*')
      .eq('id', offerId)
      .single();

    if (offerError) throw offerError;
    if (!offer) throw new Error('Offer not found');

    // Dobijamo business podatke
    const { data: business, error: businessError } = await supabase
      .from('businesses')
      .select('id, company_name')
      .eq('id', offer.business_id)
      .single();

    if (businessError) throw businessError;

    // Dobijamo business kategorije
    const { data: businessCategories } = await supabase
      .from('business_target_categories')
      .select(
        `
        categories (
          id,
          name,
          icon
        )
      `
      )
      .eq('business_id', offer.business_id);

    const categories = businessCategories?.map(bc => bc.categories) || [];

    // Dobijamo business profile podatke
    const { data: businessProfile, error: businessProfileError } =
      await supabase
        .from('profiles')
        .select('avatar_url, username')
        .eq('id', offer.business_id)
        .single();

    if (businessProfileError) throw businessProfileError;

    // Dobijamo influencer profile podatke
    const { data: influencerProfile, error: influencerError } = await supabase
      .from('profiles')
      .select('id, username, full_name, public_display_name, avatar_url')
      .eq('id', offer.influencer_id)
      .single();

    if (influencerError) throw influencerError;

    // Kombinujemo sve podatke
    const offerWithDetails = {
      ...offer,
      businesses: {
        ...business,
        categories: categories,
        profiles: businessProfile,
      },
      influencer: influencerProfile,
    };

    return { data: offerWithDetails as DirectOfferWithDetails, error: null };
  } catch (error) {
    console.error('Error fetching direct offer:', error);
    return { data: null, error };
  }
}

// Provjera da li korisnik može pristupiti ponudi
export async function canAccessOffer(offerId: string) {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) return false;

    const { data, error } = await supabase
      .from('direct_offers')
      .select(
        `
        id,
        businesses!inner(user_id),
        influencers!inner(user_id)
      `
      )
      .eq('id', offerId)
      .single();

    if (error || !data) return false;

    // Provjeri da li je korisnik vlasnik biznisa ili influencer
    return (
      data.businesses.user_id === user.user.id ||
      data.influencers.user_id === user.user.id
    );
  } catch (error) {
    console.error('Error checking offer access:', error);
    return false;
  }
}

// Kreiranje Stripe checkout session za plaćanje ponude
export async function createOfferPaymentSession(
  offerId: string,
  budget: number
) {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) throw new Error('User not authenticated');

    const response = await fetch('/api/stripe/create-offer-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.access_token}`,
      },
      body: JSON.stringify({
        offerId,
        budget,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create payment session');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating offer payment session:', error);
    throw error;
  }
}

// Proverava da li je ponuda plaćena (koristi requirements polje kao privremeno rešenje)
// DEPRECATED: Use the new async isOfferPaid(offerId) function instead
export function isOfferPaidSync(offer: DirectOffer): boolean {
  if (!offer.requirements) return false;
  return offer.requirements.includes('[PAYMENT_INFO]');
}

// Dobija payment info iz ponude
// DEPRECATED: Use the new async getOfferPaymentInfo(offerId) function instead
export function getPaymentInfoSync(offer: DirectOffer): PaymentInfo | null {
  if (!offer.requirements || !offer.requirements.includes('[PAYMENT_INFO]')) {
    return null;
  }

  try {
    const paymentStart =
      offer.requirements.indexOf('[PAYMENT_INFO]') + '[PAYMENT_INFO]'.length;
    const paymentJson = offer.requirements.slice(paymentStart).split('\n')[0];
    return JSON.parse(paymentJson);
  } catch (error) {
    console.error('Error parsing payment info:', error);
    return null;
  }
}

// Statistike za dashboard
export async function getOfferStats() {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) throw new Error('User not authenticated');

    // Provjeri tip korisnika
    const { data: businessData } = await supabase
      .from('businesses')
      .select('id')
      .eq('user_id', user.user.id)
      .single();

    const { data: influencerData } = await supabase
      .from('influencers')
      .select('id')
      .eq('user_id', user.user.id)
      .single();

    if (businessData) {
      // Statistike za biznis
      const { data: stats, error } = await supabase
        .from('direct_offers')
        .select('status')
        .eq('business_id', businessData.id);

      if (error) throw error;

      const total = stats?.length || 0;
      const pending = stats?.filter(s => s.status === 'pending').length || 0;
      const accepted = stats?.filter(s => s.status === 'accepted').length || 0;
      const rejected = stats?.filter(s => s.status === 'rejected').length || 0;

      return {
        data: { total, pending, accepted, rejected },
        error: null,
        userType: 'business',
      };
    } else if (influencerData) {
      // Statistike za influencera
      const { data: stats, error } = await supabase
        .from('direct_offers')
        .select('status')
        .eq('influencer_id', influencerData.id);

      if (error) throw error;

      const total = stats?.length || 0;
      const pending = stats?.filter(s => s.status === 'pending').length || 0;
      const accepted = stats?.filter(s => s.status === 'accepted').length || 0;
      const rejected = stats?.filter(s => s.status === 'rejected').length || 0;

      return {
        data: { total, pending, accepted, rejected },
        error: null,
        userType: 'influencer',
      };
    }

    return { data: null, error: 'User type not found', userType: null };
  } catch (error) {
    console.error('Error fetching offer stats:', error);
    return { data: null, error, userType: null };
  }
}

// OPTIMIZED FUNCTIONS FOR BUSINESS OFFERS CARDS

interface OfferCard {
  id: string;
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  offer_type: string;
  created_at: string;
  deadline: string | null;
  influencer_username: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
}

interface OfferStats {
  total_count: number;
  pending_count: number;
  accepted_count: number;
  rejected_count: number;
  completed_count: number;
  cancelled_count: number;
}

// Optimized function for loading business offers cards (fast!)
export async function getBusinessOffersCards(
  businessId: string,
  status?: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ data: OfferCard[] | null; error: unknown }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Call optimized RPC function
    const { data, error } = await supabase.rpc('get_business_offers_cards', {
      p_business_id: businessId,
      p_status: status || null,
      p_limit: limit,
      p_offset: offset,
    });

    if (error) {
      console.error('Error fetching offer cards:', error);
      return { data: null, error };
    }

    return { data: data || [], error: null };
  } catch (error: unknown) {
    console.error('Error fetching offer cards:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Get offer stats for tabs
export async function getBusinessOffersStats(
  businessId: string
): Promise<{ data: OfferStats | null; error: unknown }> {
  try {
    const { data, error } = await supabase.rpc(
      'count_business_offers_by_status',
      {
        p_business_id: businessId,
      }
    );

    if (error) {
      console.error('Error fetching offer stats:', error);
      return { data: null, error };
    }

    // RPC returns array with single object
    const stats =
      data && data.length > 0
        ? data[0]
        : {
            total_count: 0,
            pending_count: 0,
            accepted_count: 0,
            rejected_count: 0,
            completed_count: 0,
            cancelled_count: 0,
          };

    return { data: stats, error: null };
  } catch (error: unknown) {
    console.error('Error fetching offer stats:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

interface OfferDetails {
  id: string;
  business_id: string;
  influencer_id: string;
  title: string;
  description: string;
  budget: number;
  content_types: string[];
  platforms: string[];
  deadline: string | null;
  requirements: string;
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  offer_type: string;
  package_id: number | null;
  business_message: string;
  influencer_response: string;
  created_at: string;
  updated_at: string;
  responded_at: string | null;
  accepted_at: string | null;
  rejected_at: string | null;
  deliverables: string;
  influencer_username: string;
  influencer_full_name: string;
  influencer_display_name: string;
  influencer_avatar_url: string | null;
  influencer_bio: string;
}

// Lazy loading function for offer details (only when user clicks)
export async function getOfferDetailsOptimized(
  offerId: string
): Promise<{ data: OfferDetails | null; error: unknown }> {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return { data: null, error: { message: 'User not authenticated' } };
    }

    // Call RPC function for detailed data
    const { data, error } = await supabase.rpc('get_offer_details', {
      p_offer_id: offerId,
    });

    if (error) {
      console.error('Error fetching offer details:', error);
      return { data: null, error };
    }

    // RPC returns array with single object
    const details = data && data.length > 0 ? data[0] : null;

    return { data: details, error: null };
  } catch (error: unknown) {
    console.error('Error fetching offer details:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// ========================== NEW PAYMENT SYSTEM ===========================
// Helper functions for new payments table (replacing old requirements field logic)

/**
 * Check if an offer is paid using the payments table
 */
export async function isOfferPaid(offerId: string): Promise<boolean> {
  try {
    const { data: payment } = await supabase
      .from('payments')
      .select('id')
      .eq('direct_offer_id', offerId)
      .eq('payment_status', 'completed')
      .maybeSingle();

    return !!payment;
  } catch (error) {
    console.error('Error checking offer payment status:', error);
    return false;
  }
}

/**
 * Get payment info for an offer from the payments table
 */
export async function getOfferPaymentInfo(
  offerId: string
): Promise<any | null> {
  try {
    const { data: payment } = await supabase
      .from('payments')
      .select('*')
      .eq('direct_offer_id', offerId)
      .eq('payment_status', 'completed')
      .maybeSingle();

    return payment;
  } catch (error) {
    console.error('Error fetching offer payment info:', error);
    return null;
  }
}

// ========================== OLD PAYMENT SYSTEM (DEPRECATED) ===========================
// These functions are kept for backward compatibility but should be phased out

/**
 * @deprecated Use isOfferPaid(offerId) instead - this uses the new payments table
 */
export function isOfferPaidLegacy(offer: DirectOffer): boolean {
  if (!offer.requirements) return false;
  return offer.requirements.includes('[PAYMENT_INFO]');
}

/**
 * @deprecated Use getOfferPaymentInfo(offerId) instead - this uses the new payments table
 */
export function getPaymentInfoLegacy(offer: DirectOffer): PaymentInfo | null {
  if (!offer.requirements || !offer.requirements.includes('[PAYMENT_INFO]')) {
    return null;
  }

  try {
    const paymentStart =
      offer.requirements.indexOf('[PAYMENT_INFO]') + '[PAYMENT_INFO]'.length;
    const paymentJson = offer.requirements.slice(paymentStart).split('\n')[0];
    return JSON.parse(paymentJson);
  } catch (error) {
    console.error('Error parsing payment info:', error);
    return null;
  }
}
