# Supabase MCP Server - Instrukcije za instalaciju

## Što je MCP?
Model Context Protocol (MCP) omogućava AI asistentima (poput Claude-a) da direktno pristupaju vašoj Supabase bazi podataka i izvršavaju operacije poput:
- Čitanja podataka iz tabela
- Izvršavanja SQL upita
- Analiziranja strukture baze podataka
- Generiranja TypeScript tipova
- Upravljanja migracijama

## Korak 1: Kreiranje Personal Access Token-a

1. Idite na: https://supabase.com/dashboard/account/tokens
2. Kliknite "Create new token"
3. Dajte mu naziv: "MCP Server Token"
4. Kopirajte token (VAŽNO: nećete ga više vidjeti!)

## Korak 2: Pronalaženje Service Role Key-a

1. Idite na: https://supabase.com/dashboard/project/awxxrkyommynqlcdwwon/settings/api
2. U sekciji "Project API keys" pronađite "service_role" key
3. <PERSON><PERSON><PERSON><PERSON> taj key

## Korak 3: Ažuriranje .env.local datoteke

Zamijenite sljedeće vrijednosti u `.env.local`:

```bash
# Zamijenite ovu liniju:
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Sa vašim pravim service role key-em:
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# I dodajte personal access token:
SUPABASE_ACCESS_TOKEN=sbp_1234567890abcdef...
```

## Korak 4: Konfiguracija MCP klijenta

### Za Claude Desktop (preporučeno):

1. Instalirajte Claude Desktop aplikaciju
2. Otvorite konfiguraciju (obično u `~/.config/claude/claude_desktop_config.json`)
3. Kopirajte sadržaj iz `mcp-config.json` (Linux/Mac) ili `mcp-config-windows.json` (Windows)
4. Zamijenite `your_personal_access_token_here` sa vašim pravim token-om

### Za Cursor:

1. Otvorite Cursor settings
2. Idite na "MCP Servers"
3. Dodajte novu konfiguraciju koristeći sadržaj iz odgovarajuće config datoteke

## Korak 5: Testiranje

Pokrenite sljedeću komandu da testirate da li MCP server radi:

```bash
npx -y @supabase/mcp-server-supabase@latest --read-only --project-ref=awxxrkyommynqlcdwwon --access-token=********************************************
```

Ili koristite pripremljene test skripte:
```bash
# Windows
test-mcp.bat

# Linux/Mac
./test-mcp.sh
```

**NAPOMENA:** Koristimo `--access-token` flag umjesto environment varijable jer MCP server ne čita `.env.local` datoteke automatski.

## Sigurnosne preporuke

- **Read-only mode**: Koristimo `--read-only` flag za sigurnost
- **Project scoping**: Ograničavamo pristup samo na vaš projekt
- **Ne koristite u produkciji**: Koristite samo za development
- **Čuvajte token-e**: Nikad ne dijelite access token-e javno

## Dostupni alati

Kada je MCP server konfiguriran, AI asistent će imati pristup sljedećim alatima:

### Database alati:
- `list_tables` - Lista svih tabela
- `execute_sql` - Izvršavanje SQL upita
- `apply_migration` - Primjena migracija
- `generate_typescript_types` - Generiranje TS tipova

### Development alati:
- `get_project_url` - API URL projekta
- `get_anon_key` - Anonymous API key
- `search_docs` - Pretraživanje Supabase dokumentacije

### Debug alati:
- `get_logs` - Logovi servisa
- `get_advisors` - Sigurnosni savjeti

## Troubleshooting

### Problem: "Unknown option --help"
- Ovo je normalno, MCP server ne podržava --help flag

### Problem: "Access token invalid"
- Provjerite da li je token ispravno kopiran
- Provjerite da li token nije istekao

### Problem: "Project not found"
- Provjerite project reference ID: `awxxrkyommynqlcdwwon`
- Provjerite da li imate pristup projektu

## Sljedeći koraci

Nakon uspješne konfiguracije, možete:
1. Pitati AI asistenta da analizira vašu bazu podataka
2. Generirati TypeScript tipove
3. Optimizirati SQL upite
4. Kreirati migracije
5. Debugirati probleme u bazi
