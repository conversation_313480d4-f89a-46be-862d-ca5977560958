import { supabase } from './supabase';

export type SubscriptionType = 'free' | 'premium';

export interface SubscriptionLimits {
  activeCampaigns: number;
  canViewApplications: boolean;
  canViewApplicationDetails: boolean;
}

export const SUBSCRIPTION_LIMITS = {
  free: {
    business: {
      activeCampaigns: 1,
      monthlyCustomOffers: 3,
      canViewApplications: false,
      canViewApplicationDetails: false,
    },
    influencer: {
      monthlyApplications: 5,
      premiumCampaignsAccess: false,
    },
  },
  premium: {
    business: {
      activeCampaigns: -1, // unlimited
      monthlyCustomOffers: -1, // unlimited
      canViewApplications: true,
      canViewApplicationDetails: true,
    },
    influencer: {
      monthlyApplications: -1, // unlimited
      premiumCampaignsAccess: true,
    },
  },
} as const;

export interface CampaignActivationResult {
  canActivate: boolean;
  reason?: string;
  activeCount: number;
  maxAllowed: number;
}

export async function canActivateCampaign(
  businessId: string
): Promise<CampaignActivationResult> {
  try {
    // Check new subscription system first
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('status')
      .eq('user_id', businessId)
      .eq('user_type', 'business')
      .eq('status', 'active')
      .single();

    // Get business subscription info (fallback to old system)
    const { data: business, error } = await supabase
      .from('businesses')
      .select('subscription_type, active_campaigns_count')
      .eq('id', businessId)
      .single();

    if (error || !business) {
      throw new Error('Failed to fetch business data');
    }

    // Use new subscription system if available, otherwise fallback
    const isPremium =
      !!subscription || business.subscription_type === 'premium';
    const currentActiveCount = business.active_campaigns_count || 0;

    // If premium (unlimited campaigns)
    if (isPremium) {
      return {
        canActivate: true,
        activeCount: currentActiveCount,
        maxAllowed: -1,
      };
    }

    // For free users - use defined limit
    const maxCampaigns = SUBSCRIPTION_LIMITS.free.business.activeCampaigns;
    const canActivate = currentActiveCount < maxCampaigns;

    return {
      canActivate,
      reason: canActivate ? undefined : 'free_limit_reached',
      activeCount: currentActiveCount,
      maxAllowed: maxCampaigns,
    };
  } catch (error) {
    console.error('Error checking campaign activation:', error);
    return {
      canActivate: false,
      reason: 'error',
      activeCount: 0,
      maxAllowed: SUBSCRIPTION_LIMITS.free.business.activeCampaigns,
    };
  }
}

export async function activateCampaign(
  campaignId: string,
  businessId: string
): Promise<boolean> {
  try {
    // First check if we can activate
    const activationCheck = await canActivateCampaign(businessId);
    if (!activationCheck.canActivate) {
      return false;
    }

    // Begin transaction-like operations
    // 1. Update campaign status and set activation timestamp
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        status: 'active',
        activated_at: new Date().toISOString(),
        is_marketplace_active: true, // Also enable marketplace visibility
      })
      .eq('id', campaignId);

    if (campaignError) {
      throw campaignError;
    }

    // 2. Increment active campaigns count
    const { error: businessError } = await supabase
      .from('businesses')
      .update({
        active_campaigns_count: activationCheck.activeCount + 1,
      })
      .eq('id', businessId);

    if (businessError) {
      // Rollback campaign status if business update fails
      await supabase
        .from('campaigns')
        .update({
          status: 'draft',
          activated_at: null,
          is_marketplace_active: false,
        })
        .eq('id', campaignId);

      throw businessError;
    }

    return true;
  } catch (error) {
    console.error('Error activating campaign:', error);
    return false;
  }
}

export async function deactivateCampaign(
  campaignId: string,
  businessId: string
): Promise<boolean> {
  try {
    // Get current business active count
    const { data: business } = await supabase
      .from('businesses')
      .select('active_campaigns_count')
      .eq('id', businessId)
      .single();

    // 1. Update campaign status
    const { error: campaignError } = await supabase
      .from('campaigns')
      .update({
        status: 'draft',
        activated_at: null,
      })
      .eq('id', campaignId);

    if (campaignError) {
      throw campaignError;
    }

    // 2. Decrement active campaigns count (ensure it doesn't go below 0)
    const currentCount = business?.active_campaigns_count || 0;
    const { error: businessError } = await supabase
      .from('businesses')
      .update({
        active_campaigns_count: Math.max(0, currentCount - 1),
      })
      .eq('id', businessId);

    if (businessError) {
      throw businessError;
    }

    return true;
  } catch (error) {
    console.error('Error deactivating campaign:', error);
    return false;
  }
}

export async function toggleCampaignMarketplaceVisibility(
  campaignId: string,
  isVisible: boolean
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('campaigns')
      .update({
        is_marketplace_active: isVisible,
      })
      .eq('id', campaignId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error toggling marketplace visibility:', error);
    return false;
  }
}

export function canViewApplications(
  subscriptionType: SubscriptionType
): boolean {
  return SUBSCRIPTION_LIMITS[subscriptionType].business.canViewApplications;
}

export function canViewApplicationDetails(
  subscriptionType: SubscriptionType
): boolean {
  return SUBSCRIPTION_LIMITS[subscriptionType].business
    .canViewApplicationDetails;
}

// New subscription-aware limit functions

// Check if influencer can apply to campaigns
export async function canInfluencerApply(userId: string): Promise<{
  canApply: boolean;
  reason?: string;
  currentCount?: number;
  maxAllowed?: number;
}> {
  try {
    const hasActiveSubscription = await hasActivePremiumSubscription(
      userId,
      'influencer'
    );

    if (hasActiveSubscription) {
      return { canApply: true };
    }

    // Check monthly application count for free users
    const currentMonth = new Date();
    const firstDayOfMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    );

    const { count, error } = await supabase
      .from('campaign_applications')
      .select('*', { count: 'exact', head: true })
      .eq('influencer_id', userId)
      .gte('applied_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Error checking application count:', error);
      return { canApply: false, reason: 'error' };
    }

    const currentCount = count || 0;
    const maxApplications = 5;

    return {
      canApply: currentCount < maxApplications,
      reason:
        currentCount >= maxApplications ? 'monthly_limit_reached' : undefined,
      currentCount,
      maxAllowed: maxApplications,
    };
  } catch (error) {
    console.error('Error checking influencer application limits:', error);
    return { canApply: false, reason: 'error' };
  }
}

// Check if influencer can receive custom offers
export async function canInfluencerReceiveOffers(
  userId: string
): Promise<boolean> {
  try {
    return await hasActivePremiumSubscription(userId, 'influencer');
  } catch (error) {
    console.error('Error checking offer reception capability:', error);
    return false;
  }
}

// Check if business can send custom offers
export async function canBusinessSendCustomOffers(userId: string): Promise<{
  canSend: boolean;
  reason?: string;
  currentCount?: number;
  maxAllowed?: number;
}> {
  try {
    const hasActiveSubscription = await hasActivePremiumSubscription(
      userId,
      'business'
    );

    if (hasActiveSubscription) {
      return { canSend: true };
    }

    // Check monthly custom offer count for free users
    const currentMonth = new Date();
    const firstDayOfMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    );

    const { count, error } = await supabase
      .from('direct_offers')
      .select('*', { count: 'exact', head: true })
      .eq('business_id', userId)
      .gte('created_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Error checking custom offer count:', error);
      return { canSend: false, reason: 'error' };
    }

    const currentCount = count || 0;
    const maxOffers = SUBSCRIPTION_LIMITS.free.business.monthlyCustomOffers;

    return {
      canSend: currentCount < maxOffers,
      reason: currentCount >= maxOffers ? 'monthly_limit_reached' : undefined,
      currentCount,
      maxAllowed: maxOffers,
    };
  } catch (error) {
    console.error('Error checking custom offer limits:', error);
    return { canSend: false, reason: 'error' };
  }
}

// New subscription system types and functions

export interface SubscriptionPlan {
  id: string;
  plan_name: string;
  user_type: 'business' | 'influencer';
  price: number;
  duration_months: number;
  features: Record<string, any>;
  stripe_price_id: string;
  is_active: boolean;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  user_type: 'business' | 'influencer';
  subscription_plan_id: string;
  stripe_subscription_id: string;
  status: 'active' | 'cancelled' | 'past_due' | 'unpaid' | 'expired';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

// Get available subscription plans
export async function getSubscriptionPlans(
  userType: 'business' | 'influencer'
): Promise<SubscriptionPlan[]> {
  const { data, error } = await supabase
    .from('subscription_plans' as any)
    .select('*')
    .eq('user_type', userType)
    .eq('is_active', true)
    .order('price');

  if (error) {
    console.error('Error fetching subscription plans:', error);
    return [];
  }

  return data || [];
}

// Get user's current subscription
export async function getUserSubscription(
  userId: string,
  userType: 'business' | 'influencer'
): Promise<UserSubscription | null> {
  const { data, error } = await supabase
    .from('user_subscriptions' as any)
    .select('*')
    .eq('user_id', userId)
    .eq('user_type', userType)
    .eq('status', 'active')
    .single();

  if (error) {
    // No active subscription is not an error
    if (error.code === 'PGRST116') {
      return null;
    }
    console.error('Error fetching user subscription:', error);
    return null;
  }

  return data as any;
}

// Create subscription payment session
export async function createSubscriptionPayment(
  planId: string,
  userType: 'business' | 'influencer'
) {
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    throw new Error('User not authenticated');
  }

  const response = await fetch('/api/stripe/create-subscription-payment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.access_token}`,
    },
    body: JSON.stringify({
      planId,
      userType,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create subscription payment');
  }

  return response.json();
}

// Check if user has premium subscription
export async function hasActivePremiumSubscription(
  userId: string,
  userType: 'business' | 'influencer'
): Promise<boolean> {
  const subscription = await getUserSubscription(userId, userType);
  return subscription !== null && subscription.status === 'active';
}

// Get subscription features for user
export async function getSubscriptionFeatures(
  userId: string,
  userType: 'business' | 'influencer'
): Promise<Record<string, any>> {
  const subscription = await getUserSubscription(userId, userType);

  if (!subscription || subscription.status !== 'active') {
    // Return free plan features
    if (userType === 'business') {
      return {
        max_campaigns: SUBSCRIPTION_LIMITS.free.business.activeCampaigns,
        max_custom_offers:
          SUBSCRIPTION_LIMITS.free.business.monthlyCustomOffers,
        featured_campaigns: false,
        analytics: false,
        premium_support: false,
      };
    } else {
      return {
        max_applications: 5,
        custom_offers: false,
        premium_profile: false,
        analytics: false,
        priority_support: false,
      };
    }
  }

  return (subscription as any).subscription_plans.features || {};
}

// Subscription plan definitions for display
export const SUBSCRIPTION_PLANS = {
  business: [
    {
      name: 'Free',
      price: 0,
      duration: 'zauvijek',
      features: [
        'Do 3 kampanje mjesečno',
        'Do 3 custom offer-a mjesečno',
        'Osnovne funkcije',
        'Email podrška',
      ],
      limitations: [
        'Ograničeno na 3 kampanje',
        'Ograničeno na 3 custom offer-a',
        'Nema featured kampanje',
        'Nema napredne analitike',
      ],
      planId: null,
      stripePriceId: null,
      popular: false,
    },
    {
      name: 'Premium',
      price: 29.99,
      duration: 'mjesečno',
      features: [
        'Neograničeno kampanja',
        'Featured kampanje',
        'Napredne analitike',
        'Premium podrška',
        'Prioritet u pretraživanju',
      ],
      popular: true,
      planId: 'premium_monthly',
      stripePriceId: 'price_1S5oCBAkyndIVMJtm3aOs0V7',
    },
  ],
  influencer: [
    {
      name: 'Free',
      price: 0,
      duration: 'zauvijek',
      features: ['Do 5 aplikacija mjesečno', 'Osnovni profil', 'Email podrška'],
      limitations: [
        'Ograničeno na 5 aplikacija',
        'Ne možete primati custom offer-e',
        'Osnovni profil',
        'Nema napredne analitike',
      ],
      planId: null,
      stripePriceId: null,
      popular: false,
    },
    {
      name: 'Premium',
      price: 19.99,
      duration: 'mjesečno',
      features: [
        'Neograničeno aplikacija',
        'Primanje custom offer-a',
        'Premium profil',
        'Napredne analitike',
        'Prioritet podrška',
      ],
      popular: true,
      planId: 'premium_monthly',
      stripePriceId: 'price_1S5oCHAkyndIVMJt9wQXDVGp',
    },
  ],
};
