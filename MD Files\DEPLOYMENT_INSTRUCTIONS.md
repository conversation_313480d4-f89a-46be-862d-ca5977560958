# Deployment Instructions - Featured Campaign Payment System

## 🎯 Kreiran kompletan sistem - Ready for deployment!

### 1. **Database Migration**
Fajl kreiran: `migrations/add_featured_campaign_payments.sql`

**Via Supabase MCP:**
```bash
# Koristi MCP apply_migration() function
apply_migration()  # pa aplicira SQL iz fajla
```

**Via Supabase Dashboard:**
- Idi na Database → SQL Editor
- Copy/paste sadržaj iz `migrations/add_featured_campaign_payments.sql`
- Run migration

**Via CLI:**
```bash
supabase db push
```

### 2. **Deploy Edge Function**
Fajl kreiran: `supabase/functions/create-featured-campaign-payment/index.ts`

**Via Supabase MCP:**
```bash
# Koristi MCP deploy_edge_function()
deploy_edge_function("create-featured-campaign-payment", "supabase/functions/create-featured-campaign-payment/index.ts")
```

**Via Supabase CLI:**
```bash
supabase functions deploy create-featured-campaign-payment
```

**Via Supabase Dashboard:**
- Idi na Edge Functions
- Create New Function: `create-featured-campaign-payment`
- Copy/paste kod iz index.ts fajla

### 3. **Environment Variables Check**
Ensure ove env variables su postavljene:
- `STRIPE_SECRET_KEY`
- `SUPABASE_URL` 
- `SUPABASE_SERVICE_ROLE_KEY`
- `NEXT_PUBLIC_APP_URL`

### 4. **Test Flow**
1. **Frontend**: Open campaign → click "Promoviši"
2. **API Route**: `/api/stripe/create-featured-campaign-payment` poziva Edge Function
3. **Edge Function**: kreira Stripe checkout session
4. **Stripe**: redirect na payment
5. **Webhook**: `/api/stripe/webhook` obrađuje payment
6. **Database**: payment saved u `payments` tabelu
7. **Campaign**: marked as `is_featured = true`

### 5. **Verification**
Nakon deployment:
```sql
-- Check da li je migration uspešna
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'payments' AND column_name = 'campaign_id';

SELECT column_name FROM information_schema.columns 
WHERE table_name = 'campaigns' AND column_name = 'featured_until';
```

### 6. **Logs Check**
```bash
# Edge Function logs
supabase functions logs create-featured-campaign-payment

# Ili via MCP
get_logs()  # za edge functions service
```

---

## 🎉 Status: READY FOR DEPLOYMENT!

Svi fajlovi su kreirani i sistem je implementiran. 
Samo treba deploy + migration!
